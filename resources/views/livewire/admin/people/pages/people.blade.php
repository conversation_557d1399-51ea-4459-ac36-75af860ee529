<div class="space-y-8">
    <flux:breadcrumbs>
        <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home" />
        <flux:breadcrumbs.item>{{ __('people.title') }}</flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <!-- Page Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="space-y-2">
            <flux:heading size="xl" class="text-zinc-900">{{ __('people.title') }}</flux:heading>
            <flux:subheading class="text-zinc-600">{{ __('people.title_description') }}</flux:subheading>
        </div>

        <div class="flex flex-col sm:flex-row gap-3">
            @can('create people')
                <flux:button :href="route('admin.people.create')" variant="primary" icon="plus">
                    {{ __('people.add_person') }}
                </flux:button>
            @endcan

            @can('view people')
                <flux:button :href="route('admin.people.search')" variant="outline" icon="magnifying-glass">
                    {{ __('people.advanced_search') }}
                </flux:button>
            @endcan

            @can('export people')
                <flux:button wire:click="exportPeople" variant="ghost" icon="arrow-down-tray">
                    {{ __('people.export') }}
                </flux:button>
            @endcan
        </div>
    </div>

    <!-- Main Content Card -->
    <flux:card class="space-y-6">
        <!-- Search and Filters Section -->
        <div class="space-y-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex-1 max-w-md">
                    <flux:input
                        wire:model.live.debounce.300ms="search"
                        placeholder="{{ __('people.search_by_name_enhanced') }}"
                        icon="magnifying-glass"
                    />
                </div>
                <div class="flex items-center gap-3">
                    <!-- Quick Filters -->
                    <flux:button wire:click="toggleQuickFilters" variant="ghost" icon="funnel" size="sm">
                        {{ __('people.quick_filters') }}
                    </flux:button>

                    <!-- Saved Filters -->
                    <flux:select wire:model.live="savedFilter" placeholder="{{ __('people.saved_filters') }}" size="sm">
                        @foreach($savedFilters as $filter)
                            <flux:select.option value="{{ $filter->id }}">{{ $filter->name }}</flux:select.option>
                        @endforeach
                    </flux:select>

                    <flux:button wire:click="clearFilters" variant="outline" icon="x-mark" size="sm">
                        {{ __('people.clear_filters') }}
                    </flux:button>
                </div>
            </div>

            <!-- Quick Filters Panel -->
            @if($showQuickFilters)
                <div class="bg-zinc-50 rounded-lg p-4 border border-zinc-200">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.person_type') }}</flux:label>
                            <flux:select wire:model.live="quickFilterType" placeholder="{{ __('people.all_types') }}" size="sm">
                                <flux:select.option value="militant">{{ __('people.militant') }}</flux:select.option>
                                <flux:select.option value="voter">{{ __('people.voter') }}</flux:select.option>
                                <flux:select.option value="sympathizer">{{ __('people.sympathizer') }}</flux:select.option>
                            </flux:select>
                        </flux:field>

                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.status') }}</flux:label>
                            <flux:select wire:model.live="quickFilterStatus" placeholder="{{ __('people.all_statuses') }}" size="sm">
                                <flux:select.option value="active">{{ __('people.active') }}</flux:select.option>
                                <flux:select.option value="inactive">{{ __('people.inactive') }}</flux:select.option>
                                <flux:select.option value="suspended">{{ __('people.suspended') }}</flux:select.option>
                            </flux:select>
                        </flux:field>

                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.state') }}</flux:label>
                            <flux:select wire:model.live="quickFilterState" placeholder="{{ __('people.all_states') }}" size="sm">
                                @foreach($states as $state)
                                    <flux:select.option value="{{ $state->id }}">{{ $state->name }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        </flux:field>

                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.leadership') }}</flux:label>
                            <flux:select wire:model.live="quickFilterLeadership" placeholder="{{ __('people.all') }}" size="sm">
                                <flux:select.option value="leaders">{{ __('people.only_leaders') }}</flux:select.option>
                                <flux:select.option value="non_leaders">{{ __('people.non_leaders') }}</flux:select.option>
                            </flux:select>
                        </flux:field>
                    </div>

                    <div class="flex items-center justify-between mt-4 pt-4 border-t border-zinc-200">
                        <div class="flex items-center gap-2">
                            <flux:button wire:click="saveCurrentFilter" variant="outline" icon="bookmark" size="sm">
                                {{ __('people.save_filter') }}
                            </flux:button>
                        </div>
                        <div class="text-sm text-zinc-600">
                            {{ __('people.filter_results_count', ['count' => $people->total()]) }}
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Category Tabs Section -->
        <div class="space-y-4">
            <div class="border-b border-zinc-200">
                <flux:tabs wire:model="activeTab" class="flex flex-wrap gap-6 lg:gap-8">
                    <flux:tab name="all" class="pb-4">
                        <div class="flex items-center gap-2">
                            <span class="font-medium">{{ __('people.all') }}</span>
                            <flux:badge size="sm" variant="outline">{{ $stats['total'] }}</flux:badge>
                        </div>
                    </flux:tab>
                    <flux:tab name="militants" class="pb-4">
                        <div class="flex items-center gap-2">
                            <span class="font-medium">{{ __('people.militants') }}</span>
                            <flux:badge size="sm" color="red">{{ $stats['militants'] }}</flux:badge>
                        </div>
                    </flux:tab>
                    <flux:tab name="voters" class="pb-4">
                        <div class="flex items-center gap-2">
                            <span class="font-medium">{{ __('people.voters') }}</span>
                            <flux:badge size="sm" color="blue">{{ $stats['voters'] }}</flux:badge>
                        </div>
                    </flux:tab>
                    <flux:tab name="sympathizers" class="pb-4">
                        <div class="flex items-center gap-2">
                            <span class="font-medium">{{ __('people.sympathizers') }}</span>
                            <flux:badge size="sm" color="green">{{ $stats['sympathizers'] }}</flux:badge>
                        </div>
                    </flux:tab>
                    <flux:tab name="leaders" class="pb-4">
                        <div class="flex items-center gap-2">
                            <span class="font-medium">{{ __('people.leaders_1x10') }}</span>
                            <flux:badge size="sm" color="purple">{{ $stats['leaders'] }}</flux:badge>
                        </div>
                    </flux:tab>
                </flux:tabs>
            </div>
        </div>

        <!-- Results Table Section -->
        @if($people->count() > 0)
            <div class="space-y-6">
                <div class="flex items-center justify-between">
                    <div class="text-sm font-medium text-zinc-700">
                        {{ __('people.showing_results', ['from' => $people->firstItem(), 'to' => $people->lastItem(), 'total' => $people->total()]) }}
                    </div>
                </div>

                <flux:table :paginate="$people">
                    <flux:table.columns>
                        <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">
                            {{ __('people.full_name') }}
                        </flux:table.column>
                        <flux:table.column sortable :sorted="$sortBy === 'document'" :direction="$sortDirection" wire:click="sort('document')">
                            {{ __('people.document_number') }}
                        </flux:table.column>
                        <flux:table.column sortable :sorted="$sortBy === 'type'" :direction="$sortDirection" wire:click="sort('type')">
                            {{ __('people.person_type') }}
                        </flux:table.column>
                        <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection" wire:click="sort('status')">
                            {{ __('people.status') }}
                        </flux:table.column>
                        <flux:table.column>{{ __('people.location') }}</flux:table.column>
                        <flux:table.column>{{ __('global.actions') }}</flux:table.column>
                    </flux:table.columns>
                    <flux:table.rows>
                        @foreach($people as $person)
                            <flux:table.row :key="$person->id">
                                <flux:table.cell class="flex items-center gap-3">
                                    <flux:avatar size="sm" class="bg-zinc-100 text-zinc-500">
                                        <flux:icon.user class="h-5 w-5" />
                                    </flux:avatar>
                                    <div class="min-w-0 flex-1">
                                        <div class="font-semibold text-zinc-900 truncate">{{ $person->full_name }}</div>
                                        @if($person->email)
                                            <div class="text-sm text-zinc-500 truncate">{{ $person->email }}</div>
                                        @endif
                                        @if($person->is_leader_1x10)
                                            <flux:badge size="xs" color="purple" class="mt-1">{{ __('people.leader_1x10') }}</flux:badge>
                                        @endif
                                    </div>
                                </flux:table.cell>
                                <flux:table.cell class="font-mono text-sm font-medium">{{ $person->document_number }}</flux:table.cell>
                                <flux:table.cell>
                                    <flux:badge size="sm" :color="match($person->person_type) {
                                        'militant' => 'red',
                                        'voter' => 'blue',
                                        'sympathizer' => 'green',
                                        default => 'zinc'
                                    }" inset="top bottom">
                                        {{ __('people.' . $person->person_type) }}
                                    </flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>
                                    <flux:badge size="sm" :color="match($person->status) {
                                        'active' => 'green',
                                        'inactive' => 'yellow',
                                        'suspended' => 'red',
                                        default => 'zinc'
                                    }" inset="top bottom">
                                        {{ __('people.' . $person->status) }}
                                    </flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>
                                    @if($person->state)
                                        <div class="text-sm">
                                            <div class="font-medium text-zinc-900">{{ $person->state->name }}</div>
                                            @if($person->municipality)
                                                <div class="text-zinc-500">{{ $person->municipality->name }}</div>
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-zinc-400 text-sm">{{ __('people.no_location') }}</span>
                                    @endif
                                </flux:table.cell>
                                <flux:table.cell>
                                    <div class="flex items-center justify-end gap-1">
                                        <flux:button size="sm" :href="route('admin.people.show', $person)" variant="ghost" icon="eye" tooltip="{{ __('people.view') }}">
                                            <span class="sr-only">{{ __('people.view') }}</span>
                                        </flux:button>
                                        @can('update people')
                                            <flux:button size="sm" :href="route('admin.people.edit', $person)" variant="outline" icon="pencil" tooltip="{{ __('people.edit') }}">
                                                <span class="sr-only">{{ __('people.edit') }}</span>
                                            </flux:button>
                                        @endcan
                                    </div>
                                </flux:table.cell>
                            </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            </div>
        @else
            <!-- Empty State Section -->
            <div class="text-center py-20">
                <div class="mx-auto h-20 w-20 rounded-full bg-zinc-100 flex items-center justify-center mb-6">
                    <flux:icon.users class="h-10 w-10 text-zinc-400" />
                </div>
                <flux:heading size="lg" class="text-zinc-900 mb-3">{{ __('people.no_results') }}</flux:heading>
                <flux:subheading class="text-zinc-600 mb-8 max-w-md mx-auto">{{ __('people.no_results_description') }}</flux:subheading>
                @can('create people')
                    <flux:button :href="route('admin.people.create')" variant="primary" icon="plus" size="lg">
                        {{ __('people.add_first_person') }}
                    </flux:button>
                @endcan
            </div>
        @endif
    </flux:card>
</div>
