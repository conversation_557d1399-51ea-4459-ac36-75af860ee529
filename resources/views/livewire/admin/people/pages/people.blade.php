<div class="space-y-8">
    <flux:breadcrumbs>
        <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home" />
        <flux:breadcrumbs.item>{{ __('people.title') }}</flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <!-- Page Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="space-y-2">
            <flux:heading size="xl" class="text-zinc-900">{{ __('people.title') }}</flux:heading>
            <flux:subheading class="text-zinc-600">{{ __('people.title_description') }}</flux:subheading>
        </div>

        <div class="flex flex-col sm:flex-row gap-3">
            @can('create people')
                <flux:button :href="route('admin.people.create')" variant="primary" icon="plus">
                    {{ __('people.add_person') }}
                </flux:button>
            @endcan

            @can('view people')
                <flux:button :href="route('admin.people.search')" variant="outline" icon="magnifying-glass">
                    {{ __('people.advanced_search') }}
                </flux:button>
            @endcan

            @can('export people')
                <flux:button wire:click="exportPeople" variant="ghost" icon="arrow-down-tray">
                    {{ __('people.export') }}
                </flux:button>
            @endcan
        </div>
    </div>

    <!-- Main Content Card -->
    <flux:card class="space-y-6">
        <!-- Search and Filters Section -->
        <div class="space-y-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex-1 max-w-md">
                    <flux:input
                        wire:model.live.debounce.300ms="search"
                        placeholder="{{ __('people.search_by_name_enhanced') }}"
                        icon="magnifying-glass"
                    />
                </div>
                <div class="flex items-center gap-3">
                    <!-- Quick Filters -->
                    <flux:button wire:click="toggleQuickFilters" variant="ghost" icon="funnel" size="sm">
                        {{ __('people.quick_filters') }}
                    </flux:button>

                    <!-- Saved Filters -->
                    <flux:select wire:model.live="savedFilter" placeholder="{{ __('people.saved_filters') }}" size="sm">
                        @foreach($savedFilters as $filter)
                            <flux:select.option value="{{ $filter->id }}">{{ $filter->name }}</flux:select.option>
                        @endforeach
                    </flux:select>

                    <flux:button wire:click="clearFilters" variant="outline" icon="x-mark" size="sm">
                        {{ __('people.clear_filters') }}
                    </flux:button>
                </div>
            </div>

            <!-- Quick Filters Panel -->
            @if($showQuickFilters)
                <div class="bg-zinc-50 rounded-lg p-4 border border-zinc-200">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.person_type') }}</flux:label>
                            <flux:select wire:model.live="quickFilterType" placeholder="{{ __('people.all_types') }}" size="sm">
                                <flux:select.option value="militant">{{ __('people.militant') }}</flux:select.option>
                                <flux:select.option value="voter">{{ __('people.voter') }}</flux:select.option>
                                <flux:select.option value="sympathizer">{{ __('people.sympathizer') }}</flux:select.option>
                            </flux:select>
                        </flux:field>

                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.status') }}</flux:label>
                            <flux:select wire:model.live="quickFilterStatus" placeholder="{{ __('people.all_statuses') }}" size="sm">
                                <flux:select.option value="active">{{ __('people.active') }}</flux:select.option>
                                <flux:select.option value="inactive">{{ __('people.inactive') }}</flux:select.option>
                                <flux:select.option value="suspended">{{ __('people.suspended') }}</flux:select.option>
                            </flux:select>
                        </flux:field>

                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.state') }}</flux:label>
                            <flux:select wire:model.live="quickFilterState" placeholder="{{ __('people.all_states') }}" size="sm">
                                @foreach($states as $state)
                                    <flux:select.option value="{{ $state->id }}">{{ $state->name }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        </flux:field>

                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.leadership') }}</flux:label>
                            <flux:select wire:model.live="quickFilterLeadership" placeholder="{{ __('people.all') }}" size="sm">
                                <flux:select.option value="leaders">{{ __('people.only_leaders') }}</flux:select.option>
                                <flux:select.option value="non_leaders">{{ __('people.non_leaders') }}</flux:select.option>
                            </flux:select>
                        </flux:field>
                    </div>

                    <div class="flex items-center justify-between mt-4 pt-4 border-t border-zinc-200">
                        <div class="flex items-center gap-2">
                            <flux:modal.trigger name="save-filter-modal">
                                <flux:button variant="outline" icon="bookmark" size="sm">
                                    {{ __('people.save_filter') }}
                                </flux:button>
                            </flux:modal.trigger>
                        </div>
                        <div class="text-sm text-zinc-600">
                            {{ __('people.filter_results_count', ['count' => $people->total()]) }}
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Category Tabs Section -->
        <div class="space-y-4">
            <div class="border-b border-zinc-200">
                <flux:tabs wire:model="activeTab" class="flex flex-wrap gap-6 lg:gap-8">
                    <flux:tab name="all" class="pb-4">
                        <div class="flex items-center gap-2">
                            <span class="font-medium">{{ __('people.all') }}</span>
                            <flux:badge size="sm" variant="outline">{{ $stats['total'] }}</flux:badge>
                        </div>
                    </flux:tab>
                    <flux:tab name="militants" class="pb-4">
                        <div class="flex items-center gap-2">
                            <span class="font-medium">{{ __('people.militants') }}</span>
                            <flux:badge size="sm" color="red">{{ $stats['militants'] }}</flux:badge>
                        </div>
                    </flux:tab>
                    <flux:tab name="voters" class="pb-4">
                        <div class="flex items-center gap-2">
                            <span class="font-medium">{{ __('people.voters') }}</span>
                            <flux:badge size="sm" color="blue">{{ $stats['voters'] }}</flux:badge>
                        </div>
                    </flux:tab>
                    <flux:tab name="sympathizers" class="pb-4">
                        <div class="flex items-center gap-2">
                            <span class="font-medium">{{ __('people.sympathizers') }}</span>
                            <flux:badge size="sm" color="green">{{ $stats['sympathizers'] }}</flux:badge>
                        </div>
                    </flux:tab>
                    <flux:tab name="leaders" class="pb-4">
                        <div class="flex items-center gap-2">
                            <span class="font-medium">{{ __('people.leaders_1x10') }}</span>
                            <flux:badge size="sm" color="purple">{{ $stats['leaders'] }}</flux:badge>
                        </div>
                    </flux:tab>
                </flux:tabs>
            </div>
        </div>

        <!-- Results Table Section -->
        @if($people->count() > 0)
            <div class="space-y-6">
                <!-- Table Header with Bulk Actions -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                        <div class="text-sm font-medium text-zinc-700">
                            {{ __('people.showing_results', ['from' => $people->firstItem(), 'to' => $people->lastItem(), 'total' => $people->total()]) }}
                        </div>

                        @if(count($selectedPeople) > 0)
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-blue-600 font-medium">
                                    {{ __('people.selected_count', ['count' => count($selectedPeople)]) }}
                                </span>
                                <flux:button wire:click="clearSelection" variant="ghost" icon="x-mark" size="xs">
                                    {{ __('people.clear_selection') }}
                                </flux:button>
                            </div>
                        @endif
                    </div>

                    <!-- Bulk Actions -->
                    @if(count($selectedPeople) > 0)
                        <div class="flex items-center gap-2">
                            <flux:modal.trigger name="bulk-edit-modal">
                                <flux:button variant="outline" icon="pencil" size="sm">
                                    {{ __('people.bulk_edit') }}
                                </flux:button>
                            </flux:modal.trigger>

                            <flux:button wire:click="showBulkStatusModal" variant="outline" icon="arrow-path" size="sm">
                                {{ __('people.bulk_status_change') }}
                            </flux:button>

                            <flux:button wire:click="bulkExport" variant="outline" icon="arrow-down-tray" size="sm">
                                {{ __('people.bulk_export') }}
                            </flux:button>

                            @can('delete people')
                                <flux:button
                                    wire:click="confirmBulkDelete"
                                    wire:confirm="{{ __('people.confirm_bulk_delete', ['count' => count($selectedPeople)]) }}"
                                    variant="danger"
                                    icon="trash"
                                    size="sm"
                                >
                                    {{ __('people.bulk_delete') }}
                                </flux:button>
                            @endcan
                        </div>
                    @endif

                    <!-- Table Actions -->
                    @if(count($selectedPeople) === 0)
                        <div class="flex items-center gap-2">
                            <flux:modal.trigger name="import-data-modal">
                                <flux:button variant="outline" icon="arrow-up-tray" size="sm">
                                    {{ __('people.import_data') }}
                                </flux:button>
                            </flux:modal.trigger>

                            <flux:button wire:click="exportAll" variant="outline" icon="arrow-down-tray" size="sm">
                                {{ __('people.export_all') }}
                            </flux:button>

                            <flux:button wire:click="showColumnsModal" variant="ghost" icon="view-columns" size="sm">
                                {{ __('people.customize_columns') }}
                            </flux:button>
                        </div>
                    @endif
                </div>

                <flux:table :paginate="$people">
                    <flux:table.columns>
                        <flux:table.column class="w-12">
                            <flux:checkbox
                                wire:model.live="selectAll"
                                wire:click="toggleSelectAll"
                                class="rounded"
                            />
                        </flux:table.column>
                        <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">
                            {{ __('people.full_name') }}
                        </flux:table.column>
                        <flux:table.column sortable :sorted="$sortBy === 'document'" :direction="$sortDirection" wire:click="sort('document')">
                            {{ __('people.document_number') }}
                        </flux:table.column>
                        <flux:table.column sortable :sorted="$sortBy === 'type'" :direction="$sortDirection" wire:click="sort('type')">
                            {{ __('people.person_type') }}
                        </flux:table.column>
                        <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection" wire:click="sort('status')">
                            {{ __('people.status') }}
                        </flux:table.column>
                        <flux:table.column>{{ __('people.location') }}</flux:table.column>
                        <flux:table.column>{{ __('people.data_quality') }}</flux:table.column>
                        <flux:table.column>{{ __('global.actions') }}</flux:table.column>
                    </flux:table.columns>
                    <flux:table.rows>
                        @foreach($people as $person)
                            <flux:table.row :key="$person->id" class="{{ in_array($person->id, $selectedPeople) ? 'bg-blue-50 border-blue-200' : '' }}">
                                <flux:table.cell>
                                    <flux:checkbox
                                        wire:model.live="selectedPeople"
                                        value="{{ $person->id }}"
                                        class="rounded"
                                    />
                                </flux:table.cell>
                                <flux:table.cell class="flex items-center gap-3">
                                    @if(isset($person->avatar) && $person->avatar)
                                        <flux:avatar size="sm" src="{{ $person->avatar_url ?? asset('storage/avatars/' . $person->avatar) }}" />
                                    @else
                                        <flux:avatar size="sm" class="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                                            {{ strtoupper(substr($person->first_name, 0, 1) . substr($person->last_name, 0, 1)) }}
                                        </flux:avatar>
                                    @endif
                                    <div class="min-w-0 flex-1">
                                        <div class="font-semibold text-zinc-900 truncate">
                                            <a href="{{ route('admin.people.show', $person) }}" class="hover:text-blue-600">
                                                {{ $person->full_name }}
                                            </a>
                                        </div>
                                        @if($person->email)
                                            <div class="text-sm text-zinc-500 truncate">{{ $person->email }}</div>
                                        @endif
                                        <div class="flex items-center gap-2 mt-1">
                                            @if($person->is_leader_1x10)
                                                <flux:badge size="xs" color="purple">{{ __('people.leader_1x10') }}</flux:badge>
                                            @endif
                                            @if($person->phone)
                                                <flux:badge size="xs" color="green" variant="outline">{{ __('people.has_phone') }}</flux:badge>
                                            @endif
                                        </div>
                                    </div>
                                </flux:table.cell>
                                <flux:table.cell class="font-mono text-sm font-medium">{{ $person->document_number }}</flux:table.cell>
                                <flux:table.cell>
                                    <flux:badge size="sm" :color="match($person->person_type) {
                                        'militant' => 'red',
                                        'voter' => 'blue',
                                        'sympathizer' => 'green',
                                        default => 'zinc'
                                    }" inset="top bottom">
                                        {{ __('people.' . $person->person_type) }}
                                    </flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>
                                    <flux:badge size="sm" :color="match($person->status) {
                                        'active' => 'green',
                                        'inactive' => 'yellow',
                                        'suspended' => 'red',
                                        default => 'zinc'
                                    }" inset="top bottom">
                                        {{ __('people.' . $person->status) }}
                                    </flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>
                                    @if($person->state)
                                        <div class="text-sm">
                                            <div class="font-medium text-zinc-900">{{ $person->state->name }}</div>
                                            @if($person->municipality)
                                                <div class="text-zinc-500">{{ $person->municipality->name }}</div>
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-zinc-400 text-sm">{{ __('people.no_location') }}</span>
                                    @endif
                                </flux:table.cell>
                                <flux:table.cell>
                                    <div class="flex items-center gap-2">
                                        @php
                                            $completeness = $person->getDataCompleteness();
                                            $qualityColor = $completeness >= 80 ? 'green' : ($completeness >= 60 ? 'yellow' : 'red');
                                        @endphp
                                        <div class="flex items-center gap-1">
                                            <div class="w-8 bg-zinc-200 rounded-full h-2">
                                                <div class="bg-{{ $qualityColor }}-500 h-2 rounded-full" style="width: {{ $completeness }}%"></div>
                                            </div>
                                            <span class="text-xs text-zinc-600">{{ $completeness }}%</span>
                                        </div>
                                        @if($person->hasIssues())
                                            <flux:badge size="xs" color="orange" variant="outline" tooltip="{{ __('people.data_issues') }}">
                                                <flux:icon.exclamation-triangle class="h-3 w-3" />
                                            </flux:badge>
                                        @endif
                                    </div>
                                </flux:table.cell>
                                <flux:table.cell>
                                    <div class="flex items-center justify-end gap-1">
                                        <!-- Quick Actions Menu -->
                                        <div class="relative" x-data="{ open: false }">
                                            <flux:button
                                                @click="open = !open"
                                                variant="ghost"
                                                icon="ellipsis-horizontal"
                                                size="sm"
                                                tooltip="{{ __('people.quick_actions') }}"
                                            />
                                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-zinc-200 z-10">
                                                <div class="py-1">
                                                    <a href="{{ route('admin.people.show', $person) }}" class="flex items-center gap-2 px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-50">
                                                        <flux:icon.eye class="h-4 w-4" />
                                                        {{ __('people.view_details') }}
                                                    </a>
                                                    @can('update people')
                                                        <a href="{{ route('admin.people.edit', $person) }}" class="flex items-center gap-2 px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-50">
                                                            <flux:icon.pencil class="h-4 w-4" />
                                                            {{ __('people.edit') }}
                                                        </a>
                                                    @endcan
                                                    @if($person->phone)
                                                        <a href="tel:{{ $person->phone }}" class="flex items-center gap-2 px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-50">
                                                            <flux:icon.phone class="h-4 w-4" />
                                                            {{ __('people.call') }}
                                                        </a>
                                                    @endif
                                                    @if($person->email)
                                                        <a href="mailto:{{ $person->email }}" class="flex items-center gap-2 px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-50">
                                                            <flux:icon.envelope class="h-4 w-4" />
                                                            {{ __('people.send_email') }}
                                                        </a>
                                                    @endif
                                                    <button wire:click="duplicatePerson({{ $person->id }})" class="flex items-center gap-2 px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-50 w-full text-left">
                                                        <flux:icon.document-duplicate class="h-4 w-4" />
                                                        {{ __('people.duplicate') }}
                                                    </button>
                                                    @can('delete people')
                                                        <button
                                                            wire:click="deletePerson({{ $person->id }})"
                                                            wire:confirm="{{ __('people.confirm_delete_single') }}"
                                                            class="flex items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                                                        >
                                                            <flux:icon.trash class="h-4 w-4" />
                                                            {{ __('people.delete') }}
                                                        </button>
                                                    @endcan
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </flux:table.cell>
                            </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            </div>
        @else
            <!-- Empty State Section -->
            <div class="text-center py-20">
                <div class="mx-auto h-20 w-20 rounded-full bg-zinc-100 flex items-center justify-center mb-6">
                    <flux:icon.users class="h-10 w-10 text-zinc-400" />
                </div>
                <flux:heading size="lg" class="text-zinc-900 mb-3">{{ __('people.no_results') }}</flux:heading>
                <flux:subheading class="text-zinc-600 mb-8 max-w-md mx-auto">{{ __('people.no_results_description') }}</flux:subheading>
                @can('create people')
                    <flux:button :href="route('admin.people.create')" variant="primary" icon="plus" size="lg">
                        {{ __('people.add_first_person') }}
                    </flux:button>
                @endcan
            </div>
        @endif
    </flux:card>

    <!-- Bulk Operations Modals -->
    <flux:modal name="bulk-edit-modal" class="md:w-96">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">{{ __('people.bulk_edit_title') }}</flux:heading>
                <flux:text class="mt-2">{{ __('people.bulk_edit_description', ['count' => count($selectedPeople)]) }}</flux:text>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <flux:field>
                    <flux:label>{{ __('people.person_type') }}</flux:label>
                    <flux:select wire:model="bulkEditData.person_type" placeholder="{{ __('people.no_change') }}">
                        <flux:select.option value="militant">{{ __('people.militant') }}</flux:select.option>
                        <flux:select.option value="voter">{{ __('people.voter') }}</flux:select.option>
                        <flux:select.option value="sympathizer">{{ __('people.sympathizer') }}</flux:select.option>
                    </flux:select>
                </flux:field>

                <flux:field>
                    <flux:label>{{ __('people.status') }}</flux:label>
                    <flux:select wire:model="bulkEditData.status" placeholder="{{ __('people.no_change') }}">
                        <flux:select.option value="active">{{ __('people.active') }}</flux:select.option>
                        <flux:select.option value="inactive">{{ __('people.inactive') }}</flux:select.option>
                        <flux:select.option value="suspended">{{ __('people.suspended') }}</flux:select.option>
                    </flux:select>
                </flux:field>

                <flux:field>
                    <flux:label>{{ __('people.state') }}</flux:label>
                    <flux:select wire:model.live="bulkEditData.state_id" placeholder="{{ __('people.no_change') }}">
                        @foreach($states as $state)
                            <flux:select.option value="{{ $state->id }}">{{ $state->name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </flux:field>

                <flux:field>
                    <flux:label>{{ __('people.assigned_leader') }}</flux:label>
                    <flux:select wire:model="bulkEditData.assigned_leader_id" placeholder="{{ __('people.no_change') }}">
                        @foreach($availableLeaders as $leader)
                            <flux:select.option value="{{ $leader->id }}">{{ $leader->full_name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </flux:field>
            </div>

            <flux:field>
                <flux:label>{{ __('people.notes_append') }}</flux:label>
                <flux:textarea wire:model="bulkEditData.notes" placeholder="{{ __('people.notes_append_placeholder') }}" rows="3" />
            </flux:field>

            <div class="flex">
                <flux:spacer />
                <flux:button flux:modal.close variant="ghost">
                    {{ __('global.cancel') }}
                </flux:button>
                <flux:button wire:click="executeBulkEdit" variant="primary">
                    {{ __('people.apply_changes') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Import Data Modal -->
    <flux:modal name="import-data-modal" class="md:w-96">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">{{ __('people.import_data_title') }}</flux:heading>
                <flux:text class="mt-2">{{ __('people.import_data_description') }}</flux:text>
            </div>

            <div class="border-2 border-dashed border-zinc-300 rounded-lg p-8 text-center">
                <flux:icon.arrow-up-tray class="mx-auto h-12 w-12 text-zinc-400 mb-4" />
                <div class="space-y-2">
                    <flux:heading size="md">{{ __('people.drag_drop_file') }}</flux:heading>
                    <flux:subheading>{{ __('people.supported_formats') }}</flux:subheading>
                </div>
                <div class="mt-4">
                    <flux:button variant="outline" icon="folder-open">
                        {{ __('people.select_file') }}
                    </flux:button>
                </div>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-start gap-3">
                    <flux:icon.information-circle class="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                        <flux:heading size="sm" class="text-blue-900">{{ __('people.import_requirements') }}</flux:heading>
                        <ul class="text-sm text-blue-700 mt-2 space-y-1">
                            <li>• {{ __('people.import_req_1') }}</li>
                            <li>• {{ __('people.import_req_2') }}</li>
                            <li>• {{ __('people.import_req_3') }}</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="flex items-center gap-4">
                <flux:button variant="outline" icon="arrow-down-tray">
                    {{ __('people.download_template') }}
                </flux:button>
                <flux:button variant="outline" icon="document-text">
                    {{ __('people.view_import_guide') }}
                </flux:button>
            </div>

            <div class="flex">
                <flux:spacer />
                <flux:button flux:modal.close variant="ghost">
                    {{ __('global.cancel') }}
                </flux:button>
                <flux:button wire:click="processImport" variant="primary" disabled>
                    {{ __('people.start_import') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Save Filter Modal -->
    <flux:modal name="save-filter-modal" class="md:w-96">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">{{ __('people.save_filter_title') }}</flux:heading>
                <flux:text class="mt-2">{{ __('people.save_filter_description') }}</flux:text>
            </div>

            <flux:field>
                <flux:label>{{ __('people.filter_name') }}</flux:label>
                <flux:input wire:model="filterName" placeholder="{{ __('people.filter_name_placeholder') }}" />
                <flux:error name="filterName" />
            </flux:field>

            <flux:field>
                <flux:label>{{ __('people.filter_description') }}</flux:label>
                <flux:textarea wire:model="filterDescription" placeholder="{{ __('people.filter_description_placeholder') }}" rows="3" />
            </flux:field>

            <flux:field>
                <flux:checkbox wire:model="filterIsPublic">{{ __('people.make_filter_public') }}</flux:checkbox>
            </flux:field>

            <div class="flex">
                <flux:spacer />
                <flux:button flux:modal.close variant="ghost">
                    {{ __('global.cancel') }}
                </flux:button>
                <flux:button wire:click="saveFilter" variant="primary">
                    {{ __('people.save_filter') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
