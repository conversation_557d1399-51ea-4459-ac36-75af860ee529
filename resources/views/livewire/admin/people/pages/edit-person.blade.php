<div class="space-y-8">
    <flux:breadcrumbs>
        <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home" />
        <flux:breadcrumbs.item href="{{ route('admin.people.index') }}">{{ __('people.title') }}</flux:breadcrumbs.item>
        <flux:breadcrumbs.item href="{{ route('admin.people.show', $person) }}">{{ $person->full_name }}</flux:breadcrumbs.item>
        <flux:breadcrumbs.item>{{ __('people.edit_person') }}</flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <!-- Page Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="space-y-2">
            <flux:heading size="xl" class="text-zinc-900">{{ __('people.edit_person') }}</flux:heading>
            <flux:subheading class="text-zinc-600">{{ $person->full_name }}</flux:subheading>
        </div>

        <div class="flex gap-3">
            <flux:button :href="route('admin.people.show', $person)" variant="outline" icon="arrow-left">
                {{ __('global.back') }}
            </flux:button>
        </div>
    </div>

    <!-- Form Container -->
        <form wire:submit="updatePerson">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Form -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Personal Data -->
                    <flux:card class="space-y-6">
                        <div>
                            <flux:heading size="lg">{{ __('people.personal_data') }}</flux:heading>
                        </div>

                        <div class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <flux:field>
                                    <flux:label>{{ __('people.first_name') }} *</flux:label>
                                    <flux:input wire:model="firstName" placeholder="{{ __('people.first_name') }}" />
                                    <flux:error name="firstName" />
                                </flux:field>

                                <flux:field>
                                    <flux:label>{{ __('people.last_name') }} *</flux:label>
                                    <flux:input wire:model="lastName" placeholder="{{ __('people.last_name') }}" />
                                    <flux:error name="lastName" />
                                </flux:field>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <flux:field>
                                    <flux:label>{{ __('people.document_number') }} *</flux:label>
                                    <flux:input wire:model.blur="documentNumber" placeholder="{{ __('people.document_number') }}" />
                                    <flux:error name="documentNumber" />
                                </flux:field>

                                <flux:field>
                                    <flux:label>{{ __('people.birth_date') }}</flux:label>
                                    <flux:input type="date" wire:model="birthDate" />
                                    <flux:error name="birthDate" />
                                </flux:field>
                            </div>

                            <flux:field>
                                <flux:label>{{ __('people.gender') }}</flux:label>
                                <flux:select wire:model="gender" placeholder="{{ __('people.select_gender') }}">
                                    <flux:select.option value="M">{{ __('people.male') }}</flux:select.option>
                                    <flux:select.option value="F">{{ __('people.female') }}</flux:select.option>
                                    <flux:select.option value="O">{{ __('people.other') }}</flux:select.option>
                                </flux:select>
                                <flux:error name="gender" />
                            </flux:field>
                        </div>
                    </flux:card>

                    <!-- Contact Information -->
                    <flux:card class="space-y-6">
                        <div>
                            <flux:heading size="lg">{{ __('people.contact_info') }}</flux:heading>
                        </div>

                        <div class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <flux:field>
                                    <flux:label>{{ __('people.phone') }}</flux:label>
                                    <flux:input wire:model="phone" placeholder="{{ __('people.phone') }}" />
                                    <flux:error name="phone" />
                                </flux:field>

                                <flux:field>
                                    <flux:label>{{ __('people.secondary_phone') }}</flux:label>
                                    <flux:input wire:model="secondaryPhone" placeholder="{{ __('people.secondary_phone') }}" />
                                    <flux:error name="secondaryPhone" />
                                </flux:field>
                            </div>

                            <flux:field>
                                <flux:label>{{ __('people.email') }}</flux:label>
                                <flux:input type="email" wire:model.blur="email" placeholder="{{ __('people.email') }}" />
                                <flux:error name="email" />
                            </flux:field>

                            <flux:field>
                                <flux:label>{{ __('people.address') }}</flux:label>
                                <flux:textarea wire:model="address" placeholder="{{ __('people.address') }}" rows="3" />
                                <flux:error name="address" />
                            </flux:field>
                        </div>
                    </flux:card>

                    <!-- Geographic Location -->
                    <flux:card class="space-y-6">
                        <div>
                            <flux:heading size="lg">{{ __('people.location') }}</flux:heading>
                        </div>

                        <div class="space-y-4">
                            <flux:field>
                                <flux:label>{{ __('people.state') }}</flux:label>
                                <flux:select wire:model.live="stateId" placeholder="{{ __('people.select_state') }}">
                                    @foreach($states as $state)
                                        <flux:select.option value="{{ $state->id }}">{{ $state->name }}</flux:select.option>
                                    @endforeach
                                </flux:select>
                                <flux:error name="stateId" />
                            </flux:field>

                            @if($stateId)
                                <flux:field>
                                    <flux:label>{{ __('people.municipality') }}</flux:label>
                                    <flux:select wire:model.live="municipalityId" placeholder="{{ __('people.select_municipality') }}">
                                        @foreach($municipalities as $municipality)
                                            <flux:select.option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:select.option>
                                        @endforeach
                                    </flux:select>
                                    <flux:error name="municipalityId" />
                                </flux:field>
                            @endif

                            @if($municipalityId)
                                <flux:field>
                                    <flux:label>{{ __('people.parish') }}</flux:label>
                                    <flux:select wire:model.live="parishId" placeholder="{{ __('people.select_parish') }}">
                                        @foreach($parishes as $parish)
                                            <flux:select.option value="{{ $parish->id }}">{{ $parish->name }}</flux:select.option>
                                        @endforeach
                                    </flux:select>
                                    <flux:error name="parishId" />
                                </flux:field>
                            @endif

                            @if($parishId)
                                <flux:field>
                                    <flux:label>{{ __('people.voting_center') }}</flux:label>
                                    <flux:select wire:model="votingCenterId" placeholder="{{ __('people.select_voting_center') }}">
                                        @foreach($votingCenters as $center)
                                            <flux:select.option value="{{ $center->id }}">{{ $center->name }}</flux:select.option>
                                        @endforeach
                                    </flux:select>
                                    <flux:error name="votingCenterId" />
                                </flux:field>

                                <flux:field>
                                    <flux:label>{{ __('people.voting_table') }}</flux:label>
                                    <flux:input wire:model="votingTable" placeholder="{{ __('people.voting_table') }}" />
                                    <flux:error name="votingTable" />
                                </flux:field>
                            @endif
                        </div>
                    </flux:card>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Person Type and Role -->
                    <flux:card class="space-y-6">
                        <div>
                            <flux:heading size="lg">{{ __('people.role_system') }}</flux:heading>
                        </div>

                        <div class="space-y-4">
                            <flux:field>
                                <flux:label>{{ __('people.person_type') }}</flux:label>
                                <flux:select wire:model="personType">
                                    <flux:select.option value="militant">{{ __('people.militant') }}</flux:select.option>
                                    <flux:select.option value="voter">{{ __('people.voter') }}</flux:select.option>
                                    <flux:select.option value="sympathizer">{{ __('people.sympathizer') }}</flux:select.option>
                                </flux:select>
                                <flux:error name="personType" />
                            </flux:field>

                            <flux:field>
                                <flux:checkbox wire:model.live="isLeader1x10">{{ __('people.is_leader_1x10') }}</flux:checkbox>
                                <flux:error name="isLeader1x10" />
                            </flux:field>

                            @if(!$isLeader1x10 && count($availableLeaders) > 0)
                                <flux:field>
                                    <flux:label>{{ __('people.assigned_leader') }}</flux:label>
                                    <flux:select wire:model="assignedLeaderId" placeholder="{{ __('people.select_leader') }}">
                                        @foreach($availableLeaders as $leader)
                                            <flux:select.option value="{{ $leader->id }}">
                                                {{ $leader->full_name }} ({{ $leader->getAvailableSpaces() }} {{ __('people.available_spaces') }})
                                            </flux:select.option>
                                        @endforeach
                                    </flux:select>
                                    <flux:error name="assignedLeaderId" />
                                </flux:field>
                            @endif

                            <flux:field>
                                <flux:label>{{ __('people.status') }}</flux:label>
                                <flux:select wire:model="status">
                                    <flux:select.option value="active">{{ __('people.active') }}</flux:select.option>
                                    <flux:select.option value="inactive">{{ __('people.inactive') }}</flux:select.option>
                                    <flux:select.option value="suspended">{{ __('people.suspended') }}</flux:select.option>
                                </flux:select>
                                <flux:error name="status" />
                            </flux:field>
                        </div>
                    </flux:card>

                    <!-- Additional Information -->
                    <flux:card class="space-y-6">
                        <div>
                            <flux:heading size="lg">{{ __('people.additional_info') }}</flux:heading>
                        </div>

                        <div class="space-y-4">
                            <flux:field>
                                <flux:label>{{ __('people.notes') }}</flux:label>
                                <flux:textarea wire:model="notes" placeholder="{{ __('people.notes') }}" rows="4" />
                                <flux:error name="notes" />
                            </flux:field>
                        </div>
                    </flux:card>

                    <!-- Actions -->
                    <flux:card class="space-y-3">
                            <flux:button type="submit" variant="primary" class="w-full">
                                {{ __('people.update') }}
                            </flux:button>

                            <flux:button :href="route('admin.people.show', $person)" variant="outline" class="w-full">
                                {{ __('global.cancel') }}
                            </flux:button>
                    </flux:card>
                </div>
            </div>
        </form>
    </div>
</div>
