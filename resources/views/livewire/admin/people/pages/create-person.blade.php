<div class="space-y-8">
    <flux:breadcrumbs>
        <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home" />
        <flux:breadcrumbs.item href="{{ route('admin.people.index') }}">{{ __('people.title') }}</flux:breadcrumbs.item>
        <flux:breadcrumbs.item>{{ __('people.add_person') }}</flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <!-- Page Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="space-y-2">
            <flux:heading size="xl" class="text-zinc-900">{{ __('people.add_person') }}</flux:heading>
            <flux:subheading class="text-zinc-600">{{ __('people.create_description') }}</flux:subheading>
        </div>

        <div class="flex gap-3">
            <flux:button :href="route('admin.people.index')" variant="outline" icon="arrow-left">
                {{ __('global.back') }}
            </flux:button>
        </div>
    </div>

    <!-- Form Container -->
    <form wire:submit="createPerson" class="space-y-8">
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
            <!-- Main Form Content -->
            <div class="xl:col-span-3 space-y-8">
                <!-- Personal Information -->
                <flux:card class="space-y-6">
                    <div class="border-b border-zinc-200 pb-4">
                        <flux:heading size="lg" class="text-zinc-900">{{ __('people.personal_data') }}</flux:heading>
                        <flux:subheading class="text-zinc-600 mt-1">{{ __('people.personal_data_description') }}</flux:subheading>
                    </div>

                    <div class="space-y-6">
                        <!-- Name Fields -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <flux:field>
                                <flux:label class="text-sm font-medium text-zinc-700">
                                    {{ __('people.first_name') }}
                                    <span class="text-red-500">*</span>
                                </flux:label>
                                <flux:input
                                    wire:model="firstName"
                                    placeholder="{{ __('people.first_name_placeholder') }}"
                                    class="mt-1"
                                />
                                <flux:error name="firstName" />
                            </flux:field>

                            <flux:field>
                                <flux:label class="text-sm font-medium text-zinc-700">
                                    {{ __('people.last_name') }}
                                    <span class="text-red-500">*</span>
                                </flux:label>
                                <flux:input
                                    wire:model="lastName"
                                    placeholder="{{ __('people.last_name_placeholder') }}"
                                    class="mt-1"
                                />
                                <flux:error name="lastName" />
                            </flux:field>
                        </div>

                        <!-- Document and Birth Date -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <flux:field>
                                <flux:label class="text-sm font-medium text-zinc-700">
                                    {{ __('people.document_number') }}
                                    <span class="text-red-500">*</span>
                                </flux:label>
                                <flux:input
                                    wire:model.blur="documentNumber"
                                    placeholder="{{ __('people.document_number_placeholder') }}"
                                    class="mt-1 font-mono"
                                />
                                <flux:error name="documentNumber" />
                            </flux:field>

                            <flux:field>
                                <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.birth_date') }}</flux:label>
                                <flux:input
                                    type="date"
                                    wire:model="birthDate"
                                    class="mt-1"
                                />
                                <flux:error name="birthDate" />
                            </flux:field>
                        </div>

                        <!-- Gender -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <flux:field>
                                <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.gender') }}</flux:label>
                                <flux:select wire:model="gender" placeholder="{{ __('people.select_gender') }}" class="mt-1">
                                    <flux:select.option value="M">{{ __('people.male') }}</flux:select.option>
                                    <flux:select.option value="F">{{ __('people.female') }}</flux:select.option>
                                    <flux:select.option value="O">{{ __('people.other') }}</flux:select.option>
                                </flux:select>
                                <flux:error name="gender" />
                            </flux:field>
                        </div>
                    </div>
                </flux:card>

                <!-- Contact Information -->
                <flux:card class="space-y-6">
                    <div class="border-b border-zinc-200 pb-4">
                        <flux:heading size="lg" class="text-zinc-900">{{ __('people.contact_info') }}</flux:heading>
                        <flux:subheading class="text-zinc-600 mt-1">{{ __('people.contact_info_description') }}</flux:subheading>
                    </div>

                    <div class="space-y-6">
                        <!-- Phone Numbers -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <flux:field>
                                <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.phone') }}</flux:label>
                                <flux:input
                                    wire:model="phone"
                                    placeholder="{{ __('people.phone_placeholder') }}"
                                    type="tel"
                                    class="mt-1"
                                />
                                <flux:error name="phone" />
                            </flux:field>

                            <flux:field>
                                <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.secondary_phone') }}</flux:label>
                                <flux:input
                                    wire:model="secondaryPhone"
                                    placeholder="{{ __('people.secondary_phone_placeholder') }}"
                                    type="tel"
                                    class="mt-1"
                                />
                                <flux:error name="secondaryPhone" />
                            </flux:field>
                        </div>

                        <!-- Email -->
                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.email') }}</flux:label>
                            <flux:input
                                type="email"
                                wire:model.blur="email"
                                placeholder="{{ __('people.email_placeholder') }}"
                                class="mt-1"
                            />
                            <flux:error name="email" />
                        </flux:field>

                        <!-- Address -->
                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.address') }}</flux:label>
                            <flux:textarea
                                wire:model="address"
                                placeholder="{{ __('people.address_placeholder') }}"
                                rows="3"
                                class="mt-1"
                            />
                            <flux:error name="address" />
                        </flux:field>
                    </div>
                </flux:card>

                <!-- Geographic Location -->
                <flux:card class="space-y-6">
                    <div class="border-b border-zinc-200 pb-4">
                        <flux:heading size="lg" class="text-zinc-900">{{ __('people.location') }}</flux:heading>
                        <flux:subheading class="text-zinc-600 mt-1">{{ __('people.location_description') }}</flux:subheading>
                    </div>

                    <div class="space-y-6">
                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.state') }}</flux:label>
                            <flux:select wire:model.live="stateId" placeholder="{{ __('people.select_state') }}" class="mt-1">
                                @foreach($states as $state)
                                    <flux:select.option value="{{ $state->id }}">{{ $state->name }}</flux:select.option>
                                @endforeach
                            </flux:select>
                            <flux:error name="stateId" />
                        </flux:field>

                        @if($stateId)
                            <flux:field>
                                <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.municipality') }}</flux:label>
                                <flux:select wire:model.live="municipalityId" placeholder="{{ __('people.select_municipality') }}" class="mt-1">
                                    @foreach($municipalities as $municipality)
                                        <flux:select.option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:select.option>
                                    @endforeach
                                </flux:select>
                                <flux:error name="municipalityId" />
                            </flux:field>
                        @endif

                        @if($municipalityId)
                            <flux:field>
                                <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.parish') }}</flux:label>
                                <flux:select wire:model.live="parishId" placeholder="{{ __('people.select_parish') }}" class="mt-1">
                                    @foreach($parishes as $parish)
                                        <flux:select.option value="{{ $parish->id }}">{{ $parish->name }}</flux:select.option>
                                    @endforeach
                                </flux:select>
                                <flux:error name="parishId" />
                            </flux:field>
                        @endif

                        @if($parishId)
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <flux:field>
                                    <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.voting_center') }}</flux:label>
                                    <flux:select wire:model="votingCenterId" placeholder="{{ __('people.select_voting_center') }}" class="mt-1">
                                        @foreach($votingCenters as $center)
                                            <flux:select.option value="{{ $center->id }}">{{ $center->name }}</flux:select.option>
                                        @endforeach
                                    </flux:select>
                                    <flux:error name="votingCenterId" />
                                </flux:field>

                                <flux:field>
                                    <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.voting_table') }}</flux:label>
                                    <flux:input wire:model="votingTable" placeholder="{{ __('people.voting_table_placeholder') }}" class="mt-1" />
                                    <flux:error name="votingTable" />
                                </flux:field>
                            </div>
                        @endif
                    </div>
                </flux:card>
                </div>

            <!-- Sidebar -->
            <div class="xl:col-span-1 space-y-6">
                <!-- Person Classification -->
                <flux:card class="space-y-6">
                    <div class="border-b border-zinc-200 pb-4">
                        <flux:heading size="lg" class="text-zinc-900">{{ __('people.classification') }}</flux:heading>
                        <flux:subheading class="text-zinc-600 mt-1">{{ __('people.classification_description') }}</flux:subheading>
                    </div>

                    <div class="space-y-6">
                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.person_type') }}</flux:label>
                            <flux:select wire:model="personType" class="mt-1">
                                <flux:select.option value="militant">
                                    <div class="flex items-center gap-2">
                                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                                        {{ __('people.militant') }}
                                    </div>
                                </flux:select.option>
                                <flux:select.option value="voter">
                                    <div class="flex items-center gap-2">
                                        <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                        {{ __('people.voter') }}
                                    </div>
                                </flux:select.option>
                                <flux:select.option value="sympathizer">
                                    <div class="flex items-center gap-2">
                                        <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                        {{ __('people.sympathizer') }}
                                    </div>
                                </flux:select.option>
                            </flux:select>
                            <flux:error name="personType" />
                        </flux:field>

                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.status') }}</flux:label>
                            <flux:select wire:model="status" class="mt-1">
                                <flux:select.option value="active">{{ __('people.active') }}</flux:select.option>
                                <flux:select.option value="inactive">{{ __('people.inactive') }}</flux:select.option>
                                <flux:select.option value="suspended">{{ __('people.suspended') }}</flux:select.option>
                            </flux:select>
                            <flux:error name="status" />
                        </flux:field>
                    </div>
                </flux:card>

                <!-- Leadership -->
                <flux:card class="space-y-6">
                    <div class="border-b border-zinc-200 pb-4">
                        <flux:heading size="lg" class="text-zinc-900">{{ __('people.leadership_1x10') }}</flux:heading>
                        <flux:subheading class="text-zinc-600 mt-1">{{ __('people.leadership_description') }}</flux:subheading>
                    </div>

                    <div class="space-y-6">
                        <flux:field>
                            <div class="flex items-center gap-3">
                                <flux:checkbox wire:model.live="isLeader1x10" />
                                <div>
                                    <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.is_leader_1x10') }}</flux:label>
                                    <div class="text-xs text-zinc-500">{{ __('people.leader_1x10_description') }}</div>
                                </div>
                            </div>
                            <flux:error name="isLeader1x10" />
                        </flux:field>

                        @if(!$isLeader1x10 && count($availableLeaders) > 0)
                            <flux:field>
                                <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.assigned_leader') }}</flux:label>
                                <flux:select wire:model="assignedLeaderId" placeholder="{{ __('people.select_leader') }}" class="mt-1">
                                    @foreach($availableLeaders as $leader)
                                        <flux:select.option value="{{ $leader->id }}">
                                            {{ $leader->full_name }} ({{ $leader->getAvailableSpaces() }} {{ __('people.available_spaces') }})
                                        </flux:select.option>
                                    @endforeach
                                </flux:select>
                                <flux:error name="assignedLeaderId" />
                            </flux:field>
                        @endif
                    </div>
                </flux:card>

                <!-- Additional Information -->
                <flux:card class="space-y-6">
                    <div class="border-b border-zinc-200 pb-4">
                        <flux:heading size="lg" class="text-zinc-900">{{ __('people.additional_info') }}</flux:heading>
                        <flux:subheading class="text-zinc-600 mt-1">{{ __('people.additional_info_description') }}</flux:subheading>
                    </div>

                    <div class="space-y-6">
                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.notes') }}</flux:label>
                            <flux:textarea
                                wire:model="notes"
                                placeholder="{{ __('people.notes_placeholder') }}"
                                rows="4"
                                class="mt-1"
                            />
                            <flux:error name="notes" />
                        </flux:field>
                    </div>
                </flux:card>

                <!-- Form Actions -->
                <div class="sticky bottom-6">
                    <flux:card class="space-y-6">
                        <div class="border-b border-zinc-200 pb-4">
                            <flux:heading size="lg" class="text-zinc-900">{{ __('people.form_actions') }}</flux:heading>
                            <flux:subheading class="text-zinc-600 mt-1">{{ __('people.form_actions_description') }}</flux:subheading>
                        </div>

                        <div class="space-y-4">
                            <flux:button type="submit" variant="primary" class="w-full" icon="plus">
                                {{ __('people.save_person') }}
                            </flux:button>

                            <flux:button :href="route('admin.people.index')" variant="outline" class="w-full" icon="arrow-left">
                                {{ __('global.cancel') }}
                            </flux:button>
                        </div>
                    </flux:card>
                </div>
            </div>
        </div>
    </form>
</div>
