<div class="space-y-8">
    <flux:breadcrumbs>
        <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home" />
        <flux:breadcrumbs.item href="{{ route('admin.people.index') }}">{{ __('people.title') }}</flux:breadcrumbs.item>
        <flux:breadcrumbs.item>{{ $person->full_name }}</flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <!-- Person Header -->
    <div class="bg-gradient-to-r from-zinc-50 to-zinc-100 rounded-xl p-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div class="flex items-center gap-6">
                <!-- Avatar -->
                <div class="flex-shrink-0">
                    <flux:avatar size="xl" class="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                        {{ strtoupper(substr($person->first_name, 0, 1) . substr($person->last_name, 0, 1)) }}
                    </flux:avatar>
                </div>

                <!-- Person Info -->
                <div class="space-y-2">
                    <div class="flex items-center gap-3">
                        <flux:heading size="xl" class="text-zinc-900">{{ $person->full_name }}</flux:heading>
                        @if($person->is_leader_1x10)
                            <flux:badge color="purple" size="sm">{{ __('people.leader_1x10') }}</flux:badge>
                        @endif
                    </div>

                    <div class="flex flex-wrap items-center gap-4 text-sm text-zinc-600">
                        <div class="flex items-center gap-1">
                            <flux:icon.identification class="w-4 h-4" />
                            <span>{{ $person->document_number }}</span>
                        </div>

                        @if($person->age)
                            <div class="flex items-center gap-1">
                                <flux:icon.calendar class="w-4 h-4" />
                                <span>{{ $person->age }} {{ __('people.years_old') }}</span>
                            </div>
                        @endif

                        @if($person->email)
                            <div class="flex items-center gap-1">
                                <flux:icon.envelope class="w-4 h-4" />
                                <span>{{ $person->email }}</span>
                            </div>
                        @endif

                        @if($person->phone)
                            <div class="flex items-center gap-1">
                                <flux:icon.phone class="w-4 h-4" />
                                <span>{{ $person->phone }}</span>
                            </div>
                        @endif
                    </div>

                    <!-- Status Badges -->
                    <div class="flex flex-wrap gap-2 mt-3">
                        <flux:badge :color="match($person->person_type) {
                            'militant' => 'red',
                            'voter' => 'blue',
                            'sympathizer' => 'green',
                            default => 'zinc'
                        }" size="sm">
                            {{ __('people.' . $person->person_type) }}
                        </flux:badge>

                        <flux:badge :color="match($person->status) {
                            'active' => 'green',
                            'inactive' => 'yellow',
                            'suspended' => 'red',
                            default => 'zinc'
                        }" size="sm">
                            {{ __('people.' . $person->status) }}
                        </flux:badge>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-3">
                @can('update people')
                    <flux:button :href="route('admin.people.edit', $person)" variant="primary" icon="pencil">
                        {{ __('people.edit') }}
                    </flux:button>
                @endcan

                @can('create users')
                    @if(!$person->user && $person->email)
                        <flux:button wire:click="createUser" variant="outline" icon="user-plus">
                            {{ __('people.create_user') }}
                        </flux:button>
                    @endif
                @endcan

                <flux:button :href="route('admin.people.index')" variant="ghost" icon="arrow-left">
                    {{ __('global.back') }}
                </flux:button>
            </div>
        </div>
    </div>

    <div class="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Personal Information -->
            <flux:card class="space-y-6">
                <div class="border-b border-zinc-200 pb-4">
                    <flux:heading size="lg" class="text-zinc-900">{{ __('people.personal_data') }}</flux:heading>
                    <flux:subheading class="text-zinc-600 mt-1">{{ __('people.personal_data_description') }}</flux:subheading>
                </div>

                <div>
                    <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <dt class="text-sm font-medium text-zinc-500">{{ __('people.first_name') }}</dt>
                            <dd class="mt-2 text-sm font-medium text-zinc-900">{{ $person->first_name }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-zinc-500">{{ __('people.last_name') }}</dt>
                            <dd class="mt-2 text-sm font-medium text-zinc-900">{{ $person->last_name }}</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-zinc-500">{{ __('people.document_number') }}</dt>
                            <dd class="mt-2 text-sm font-mono font-medium text-zinc-900">{{ $person->document_number }}</dd>
                        </div>

                        @if($person->birth_date)
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.birth_date') }}</dt>
                                <dd class="mt-2 text-sm font-medium text-zinc-900">
                                    {{ $person->birth_date->format('d/m/Y') }}
                                    @if($person->age)
                                        <span class="text-zinc-500">({{ $person->age }} {{ __('people.years_old') }})</span>
                                    @endif
                                </dd>
                            </div>
                        @endif

                        @if($person->gender)
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.gender') }}</dt>
                                <dd class="mt-2 text-sm font-medium text-zinc-900">
                                    @if($person->gender === 'M')
                                        {{ __('people.male') }}
                                    @elseif($person->gender === 'F')
                                        {{ __('people.female') }}
                                    @else
                                        {{ __('people.other') }}
                                    @endif
                                </dd>
                            </div>
                        @endif
                    </dl>
                </div>
            </flux:card>

            <!-- Contact Information -->
            <flux:card class="space-y-6">
                <div>
                    <flux:heading size="lg">{{ __('people.contact_info') }}</flux:heading>
                </div>

                <div>
                    <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @if($person->phone)
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.phone') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">
                                    <a href="tel:{{ $person->phone }}" class="text-blue-600 hover:text-blue-800">
                                        {{ $person->phone }}
                                    </a>
                                </dd>
                            </div>
                        @endif

                        @if($person->secondary_phone)
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.secondary_phone') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">
                                    <a href="tel:{{ $person->secondary_phone }}" class="text-blue-600 hover:text-blue-800">
                                        {{ $person->secondary_phone }}
                                    </a>
                                </dd>
                            </div>
                        @endif

                        @if($person->email)
                            <div class="md:col-span-2">
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.email') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">
                                    <a href="mailto:{{ $person->email }}" class="text-blue-600 hover:text-blue-800">
                                        {{ $person->email }}
                                    </a>
                                </dd>
                            </div>
                        @endif

                        @if($person->address)
                            <div class="md:col-span-2">
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.address') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">{{ $person->address }}</dd>
                            </div>
                        @endif
                    </dl>
                </div>
            </flux:card>

            <!-- Geographic Location -->
            <flux:card class="space-y-6">
                <div>
                    <flux:heading size="lg">{{ __('people.location') }}</flux:heading>
                </div>

                <div>
                    <dl class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        @if($person->state)
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.state') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">{{ $person->state->name }}</dd>
                            </div>
                        @endif

                        @if($person->municipality)
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.municipality') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">{{ $person->municipality->name }}</dd>
                            </div>
                        @endif

                        @if($person->parish)
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.parish') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">{{ $person->parish->name }}</dd>
                            </div>
                        @endif

                        @if($person->votingCenter)
                            <div class="md:col-span-2">
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.voting_center') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">{{ $person->votingCenter->name }}</dd>
                            </div>
                        @endif

                        @if($person->voting_table)
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.voting_table') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">{{ $person->voting_table }}</dd>
                            </div>
                        @endif
                    </dl>
                </div>
            </flux:card>

            @if($person->is_leader_1x10 && $person->assignedPeople->count() > 0)
                <!-- Assigned People (for leaders) -->
                <flux:card class="space-y-6">
                    <div>
                        <flux:heading size="lg">
                            {{ __('people.assigned_people') }} ({{ $person->assignedPeople->count() }}/10)
                        </flux:heading>
                    </div>

                    <div>
                        <div class="space-y-3">
                            @foreach($person->assignedPeople as $assignedPerson)
                                <div class="flex items-center justify-between p-3 bg-zinc-50 rounded-lg">
                                    <div>
                                        <div class="font-medium">{{ $assignedPerson->full_name }}</div>
                                        <div class="text-sm text-zinc-500">{{ $assignedPerson->document_number }}</div>
                                    </div>
                                    <flux:button :href="route('admin.people.show', $assignedPerson)" variant="outline" size="sm">
                                        {{ __('people.view_details') }}
                                    </flux:button>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </flux:card>
            @endif

            @if($person->notes)
                <!-- Notes -->
                <flux:card class="space-y-6">
                    <div>
                        <flux:heading size="lg">{{ __('people.notes') }}</flux:heading>
                    </div>

                    <div>
                        <p class="text-sm text-zinc-900 whitespace-pre-wrap">{{ $person->notes }}</p>
                    </div>
                </flux:card>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status and Type -->
            <flux:card class="space-y-6">
                <div>
                    <flux:heading size="lg">{{ __('people.status_and_type') }}</flux:heading>
                </div>

                <div class="space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-zinc-500">{{ __('people.person_type') }}</dt>
                        <dd class="mt-1">
                            <flux:badge
                                :color="$person->person_type === 'militant' ? 'red' : ($person->person_type === 'voter' ? 'blue' : 'green')"
                                size="sm"
                            >
                                @if($person->person_type === 'militant')
                                    {{ __('people.militant') }}
                                @elseif($person->person_type === 'voter')
                                    {{ __('people.voter') }}
                                @else
                                    {{ __('people.sympathizer') }}
                                @endif
                            </flux:badge>
                        </dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-zinc-500">{{ __('people.status') }}</dt>
                        <dd class="mt-1">
                            <flux:badge
                                :color="$person->status === 'active' ? 'green' : ($person->status === 'inactive' ? 'yellow' : 'red')"
                                size="sm"
                            >
                                @if($person->status === 'active')
                                    {{ __('people.active') }}
                                @elseif($person->status === 'inactive')
                                    {{ __('people.inactive') }}
                                @else
                                    {{ __('people.suspended') }}
                                @endif
                            </flux:badge>
                        </dd>
                    </div>

                    @if($person->is_leader_1x10)
                        <div>
                            <dt class="text-sm font-medium text-zinc-500">{{ __('people.leadership_1x10') }}</dt>
                            <dd class="mt-1">
                                <flux:badge color="purple" size="sm">
                                    {{ __('people.is_leader_1x10') }}
                                </flux:badge>
                                <div class="text-xs text-zinc-500 mt-1">
                                    {{ $person->assignedPeople->count() }}/10 {{ __('people.assigned_people') }}
                                </div>
                            </dd>
                        </div>
                    @endif

                    @if($person->assignedLeader)
                        <div>
                            <dt class="text-sm font-medium text-zinc-500">{{ __('people.assigned_leader') }}</dt>
                            <dd class="mt-1">
                                <flux:button :href="route('admin.people.show', $person->assignedLeader)" variant="outline" size="sm">
                                    {{ $person->assignedLeader->full_name }}
                                </flux:button>
                            </dd>
                        </div>
                    @endif
                </div>
            </flux:card>

            <!-- System User -->
            @if($person->user)
                <flux:card class="space-y-6">
                    <div>
                        <flux:heading size="lg">{{ __('people.system_user') }}</flux:heading>
                    </div>

                    <div>
                        <div class="space-y-2">
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.username') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">{{ $person->user->username }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.email') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">{{ $person->user->email }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.created_at') }}</dt>
                                <dd class="mt-1 text-sm text-zinc-900">{{ $person->user->created_at->format('d/m/Y H:i') }}</dd>
                            </div>
                        </div>
                    </div>
                </flux:card>
            @endif

            <!-- Timestamps -->
            <flux:card class="space-y-6">
                <div>
                    <flux:heading size="lg">{{ __('people.timestamps') }}</flux:heading>
                </div>

                <div class="space-y-2">
                    <div>
                        <dt class="text-sm font-medium text-zinc-500">{{ __('people.created_at') }}</dt>
                        <dd class="mt-1 text-sm text-zinc-900">{{ $person->created_at->format('d/m/Y H:i') }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-zinc-500">{{ __('people.updated_at') }}</dt>
                        <dd class="mt-1 text-sm text-zinc-900">{{ $person->updated_at->format('d/m/Y H:i') }}</dd>
                    </div>
                </div>
            </flux:card>

            <!-- Actions -->
            <flux:card class="space-y-3">
                    <flux:button :href="route('admin.people.index')" variant="outline" class="w-full">
                        {{ __('global.back') }}
                    </flux:button>

                    @can('delete people')
                        <flux:button
                            wire:click="deletePerson"
                            wire:confirm="{{ __('people.confirm_delete') }}"
                            variant="danger"
                            class="w-full"
                        >
                            {{ __('people.delete') }}
                        </flux:button>
                    @endcan
            </flux:card>
        </div>
    </div>
</div>
