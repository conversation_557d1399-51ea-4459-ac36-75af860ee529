<div class="space-y-8">
    <!-- Dashboard Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="space-y-2">
            <flux:heading size="xl" class="text-zinc-900">{{ __('people.dashboard_title') }}</flux:heading>
            <flux:subheading class="text-zinc-600">{{ __('people.dashboard_description') }}</flux:subheading>
        </div>

        <div class="flex items-center gap-3">
            <flux:select wire:model.live="timeRange" size="sm">
                <flux:select.option value="7">{{ __('people.last_7_days') }}</flux:select.option>
                <flux:select.option value="30">{{ __('people.last_30_days') }}</flux:select.option>
                <flux:select.option value="90">{{ __('people.last_90_days') }}</flux:select.option>
                <flux:select.option value="365">{{ __('people.last_year') }}</flux:select.option>
            </flux:select>
            
            <flux:button wire:click="refreshData" variant="outline" icon="arrow-path" size="sm">
                {{ __('people.refresh') }}
            </flux:button>
            
            <flux:button wire:click="exportDashboard" variant="outline" icon="arrow-down-tray" size="sm">
                {{ __('people.export_report') }}
            </flux:button>
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total People -->
        <flux:card class="space-y-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <flux:icon.users class="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                        <flux:heading size="sm" class="text-zinc-600">{{ __('people.total_people') }}</flux:heading>
                        <flux:heading size="xl" class="text-zinc-900">{{ number_format($metrics['total_people']) }}</flux:heading>
                    </div>
                </div>
                <div class="text-right">
                    <div class="flex items-center gap-1 text-sm {{ $metrics['people_growth'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        <flux:icon.arrow-trending-up class="h-4 w-4" />
                        {{ $metrics['people_growth'] }}%
                    </div>
                    <div class="text-xs text-zinc-500">{{ __('people.vs_last_period') }}</div>
                </div>
            </div>
        </flux:card>

        <!-- Active Leaders -->
        <flux:card class="space-y-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <flux:icon.star class="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                        <flux:heading size="sm" class="text-zinc-600">{{ __('people.active_leaders') }}</flux:heading>
                        <flux:heading size="xl" class="text-zinc-900">{{ number_format($metrics['active_leaders']) }}</flux:heading>
                    </div>
                </div>
                <div class="text-right">
                    <div class="flex items-center gap-1 text-sm {{ $metrics['leaders_growth'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        <flux:icon.arrow-trending-up class="h-4 w-4" />
                        {{ $metrics['leaders_growth'] }}%
                    </div>
                    <div class="text-xs text-zinc-500">{{ __('people.vs_last_period') }}</div>
                </div>
            </div>
        </flux:card>

        <!-- Data Quality Score -->
        <flux:card class="space-y-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <flux:icon.chart-bar class="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                        <flux:heading size="sm" class="text-zinc-600">{{ __('people.data_quality') }}</flux:heading>
                        <flux:heading size="xl" class="text-zinc-900">{{ $metrics['data_quality_score'] }}%</flux:heading>
                    </div>
                </div>
                <div class="text-right">
                    <div class="flex items-center gap-1 text-sm {{ $metrics['quality_trend'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        <flux:icon.arrow-trending-up class="h-4 w-4" />
                        {{ $metrics['quality_trend'] }}%
                    </div>
                    <div class="text-xs text-zinc-500">{{ __('people.vs_last_period') }}</div>
                </div>
            </div>
        </flux:card>

        <!-- Recent Activity -->
        <flux:card class="space-y-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-orange-100 rounded-lg">
                        <flux:icon.clock class="h-6 w-6 text-orange-600" />
                    </div>
                    <div>
                        <flux:heading size="sm" class="text-zinc-600">{{ __('people.recent_updates') }}</flux:heading>
                        <flux:heading size="xl" class="text-zinc-900">{{ number_format($metrics['recent_updates']) }}</flux:heading>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-xs text-zinc-500">{{ __('people.last_24_hours') }}</div>
                </div>
            </div>
        </flux:card>
    </div>

    <!-- Charts and Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- People Distribution Chart -->
        <flux:card class="space-y-6">
            <div class="border-b border-zinc-200 pb-4">
                <flux:heading size="lg" class="text-zinc-900">{{ __('people.distribution_by_type') }}</flux:heading>
                <flux:subheading class="text-zinc-600 mt-1">{{ __('people.distribution_description') }}</flux:subheading>
            </div>

            <div class="space-y-4">
                @foreach($distributionData as $type => $data)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 rounded-full bg-{{ $data['color'] }}-500"></div>
                            <span class="font-medium text-zinc-900">{{ __('people.' . $type) }}</span>
                        </div>
                        <div class="flex items-center gap-4">
                            <span class="text-sm text-zinc-600">{{ number_format($data['count']) }}</span>
                            <div class="w-24 bg-zinc-200 rounded-full h-2">
                                <div class="bg-{{ $data['color'] }}-500 h-2 rounded-full" style="width: {{ $data['percentage'] }}%"></div>
                            </div>
                            <span class="text-sm font-medium text-zinc-900 w-12 text-right">{{ $data['percentage'] }}%</span>
                        </div>
                    </div>
                @endforeach
            </div>
        </flux:card>

        <!-- Geographic Distribution -->
        <flux:card class="space-y-6">
            <div class="border-b border-zinc-200 pb-4">
                <flux:heading size="lg" class="text-zinc-900">{{ __('people.geographic_distribution') }}</flux:heading>
                <flux:subheading class="text-zinc-600 mt-1">{{ __('people.top_states_description') }}</flux:subheading>
            </div>

            <div class="space-y-4">
                @foreach($geographicData as $state)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <flux:icon.map-pin class="h-4 w-4 text-zinc-400" />
                            <span class="font-medium text-zinc-900">{{ $state['name'] }}</span>
                        </div>
                        <div class="flex items-center gap-4">
                            <span class="text-sm text-zinc-600">{{ number_format($state['count']) }}</span>
                            <div class="w-20 bg-zinc-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: {{ $state['percentage'] }}%"></div>
                            </div>
                            <span class="text-sm font-medium text-zinc-900 w-12 text-right">{{ $state['percentage'] }}%</span>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="pt-4 border-t border-zinc-200">
                <flux:button wire:click="showGeographicDetails" variant="outline" icon="map" class="w-full">
                    {{ __('people.view_detailed_map') }}
                </flux:button>
            </div>
        </flux:card>
    </div>

    <!-- Leadership Performance -->
    <flux:card class="space-y-6">
        <div class="border-b border-zinc-200 pb-4">
            <flux:heading size="lg" class="text-zinc-900">{{ __('people.leadership_performance') }}</flux:heading>
            <flux:subheading class="text-zinc-600 mt-1">{{ __('people.leadership_performance_description') }}</flux:subheading>
        </div>

        <div class="overflow-x-auto">
            <flux:table>
                <flux:table.columns>
                    <flux:table.column>{{ __('people.leader_name') }}</flux:table.column>
                    <flux:table.column>{{ __('people.assigned_people') }}</flux:table.column>
                    <flux:table.column>{{ __('people.completion_rate') }}</flux:table.column>
                    <flux:table.column>{{ __('people.last_activity') }}</flux:table.column>
                    <flux:table.column>{{ __('people.performance_score') }}</flux:table.column>
                </flux:table.columns>
                <flux:table.rows>
                    @foreach($leadershipData as $leader)
                        <flux:table.row>
                            <flux:table.cell class="flex items-center gap-3">
                                <flux:avatar size="sm" class="bg-gradient-to-br from-purple-500 to-blue-600 text-white">
                                    {{ strtoupper(substr($leader['first_name'], 0, 1) . substr($leader['last_name'], 0, 1)) }}
                                </flux:avatar>
                                <div>
                                    <div class="font-medium text-zinc-900">{{ $leader['full_name'] }}</div>
                                    <div class="text-sm text-zinc-500">{{ $leader['location'] }}</div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-2">
                                    <span class="font-medium">{{ $leader['assigned_count'] }}/10</span>
                                    <div class="w-16 bg-zinc-200 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: {{ ($leader['assigned_count'] / 10) * 100 }}%"></div>
                                    </div>
                                </div>
                            </flux:table.cell>
                            <flux:table.cell>
                                <flux:badge size="sm" :color="$leader['completion_rate'] >= 80 ? 'green' : ($leader['completion_rate'] >= 60 ? 'yellow' : 'red')">
                                    {{ $leader['completion_rate'] }}%
                                </flux:badge>
                            </flux:table.cell>
                            <flux:table.cell>
                                <span class="text-sm text-zinc-600">{{ $leader['last_activity'] }}</span>
                            </flux:table.cell>
                            <flux:table.cell>
                                <div class="flex items-center gap-2">
                                    <span class="font-medium">{{ $leader['performance_score'] }}</span>
                                    <flux:icon.star class="h-4 w-4 text-yellow-500" />
                                </div>
                            </flux:table.cell>
                        </flux:table.row>
                    @endforeach
                </flux:table.rows>
            </flux:table>
        </div>
    </flux:card>

    <!-- Recent Activity Feed -->
    <flux:card class="space-y-6">
        <div class="border-b border-zinc-200 pb-4">
            <flux:heading size="lg" class="text-zinc-900">{{ __('people.recent_activity') }}</flux:heading>
            <flux:subheading class="text-zinc-600 mt-1">{{ __('people.recent_activity_description') }}</flux:subheading>
        </div>

        <div class="space-y-4">
            @foreach($recentActivity as $activity)
                <div class="flex items-start gap-4 p-4 bg-zinc-50 rounded-lg">
                    <div class="flex-shrink-0">
                        <div class="p-2 bg-{{ $activity['color'] }}-100 rounded-lg">
                            <flux:icon.{{ $activity['icon'] }} class="h-4 w-4 text-{{ $activity['color'] }}-600" />
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-zinc-900">{{ $activity['title'] }}</p>
                            <span class="text-xs text-zinc-500">{{ $activity['time'] }}</span>
                        </div>
                        <p class="text-sm text-zinc-600 mt-1">{{ $activity['description'] }}</p>
                        @if($activity['user'])
                            <p class="text-xs text-zinc-500 mt-2">{{ __('people.by_user', ['user' => $activity['user']]) }}</p>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>

        <div class="pt-4 border-t border-zinc-200">
            <flux:button wire:click="showActivityLog" variant="outline" icon="clock" class="w-full">
                {{ __('people.view_full_activity_log') }}
            </flux:button>
        </div>
    </flux:card>
</div>
