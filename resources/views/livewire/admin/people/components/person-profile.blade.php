<div class="space-y-8">
    <!-- Profile Header -->
    <flux:card class="space-y-6">
        <div class="flex flex-col lg:flex-row lg:items-start gap-6">
            <!-- Avatar and Basic Info -->
            <div class="flex items-center gap-6">
                <div class="relative">
                    @if($person->avatar)
                        <flux:avatar size="2xl" src="{{ $person->avatar_url }}" />
                    @else
                        <flux:avatar size="2xl" class="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                            {{ strtoupper(substr($person->first_name, 0, 1) . substr($person->last_name, 0, 1)) }}
                        </flux:avatar>
                    @endif
                    
                    <!-- Status Indicator -->
                    <div class="absolute -bottom-1 -right-1">
                        <flux:badge size="sm" :color="match($person->status) {
                            'active' => 'green',
                            'inactive' => 'yellow',
                            'suspended' => 'red',
                            default => 'zinc'
                        }" class="border-2 border-white">
                            {{ __('people.' . $person->status) }}
                        </flux:badge>
                    </div>
                </div>

                <div class="space-y-2">
                    <div class="flex items-center gap-3">
                        <flux:heading size="xl" class="text-zinc-900">{{ $person->full_name }}</flux:heading>
                        @if($person->is_leader_1x10)
                            <flux:badge color="purple" icon="star">{{ __('people.leader_1x10') }}</flux:badge>
                        @endif
                    </div>
                    
                    <div class="flex items-center gap-4 text-sm text-zinc-600">
                        <span class="flex items-center gap-1">
                            <flux:icon.identification class="h-4 w-4" />
                            {{ $person->document_number }}
                        </span>
                        
                        @if($person->age)
                            <span class="flex items-center gap-1">
                                <flux:icon.calendar class="h-4 w-4" />
                                {{ $person->age }} {{ __('people.years_old') }}
                            </span>
                        @endif
                        
                        @if($person->phone)
                            <a href="tel:{{ $person->phone }}" class="flex items-center gap-1 hover:text-blue-600">
                                <flux:icon.phone class="h-4 w-4" />
                                {{ $person->phone }}
                            </a>
                        @endif
                        
                        @if($person->email)
                            <a href="mailto:{{ $person->email }}" class="flex items-center gap-1 hover:text-blue-600">
                                <flux:icon.envelope class="h-4 w-4" />
                                {{ $person->email }}
                            </a>
                        @endif
                    </div>

                    <!-- Person Type Badge -->
                    <div class="flex items-center gap-2">
                        <flux:badge size="sm" :color="match($person->person_type) {
                            'militant' => 'red',
                            'voter' => 'blue',
                            'sympathizer' => 'green',
                            default => 'zinc'
                        }" inset="top bottom">
                            {{ __('people.' . $person->person_type) }}
                        </flux:badge>
                        
                        @if($person->voting_center)
                            <flux:badge size="sm" color="zinc" variant="outline">
                                {{ __('people.voting_center') }}: {{ $person->voting_center->name }}
                            </flux:badge>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="lg:ml-auto flex flex-col sm:flex-row gap-3">
                @can('update people')
                    <flux:button :href="route('admin.people.edit', $person)" variant="primary" icon="pencil">
                        {{ __('people.edit') }}
                    </flux:button>
                @endcan
                
                <flux:button wire:click="showContactModal" variant="outline" icon="chat-bubble-left-right">
                    {{ __('people.contact') }}
                </flux:button>
                
                <div class="relative" x-data="{ open: false }">
                    <flux:button @click="open = !open" variant="ghost" icon="ellipsis-horizontal" />
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-zinc-200 z-10">
                        <div class="py-1">
                            <button wire:click="generatePDF" class="flex items-center gap-2 px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-50 w-full text-left">
                                <flux:icon.document-arrow-down class="h-4 w-4" />
                                {{ __('people.download_pdf') }}
                            </button>
                            <button wire:click="duplicatePerson" class="flex items-center gap-2 px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-50 w-full text-left">
                                <flux:icon.document-duplicate class="h-4 w-4" />
                                {{ __('people.duplicate') }}
                            </button>
                            <button wire:click="showAuditLog" class="flex items-center gap-2 px-4 py-2 text-sm text-zinc-700 hover:bg-zinc-50 w-full text-left">
                                <flux:icon.clock class="h-4 w-4" />
                                {{ __('people.audit_log') }}
                            </button>
                            @can('delete people')
                                <button 
                                    wire:click="deletePerson" 
                                    wire:confirm="{{ __('people.confirm_delete_single') }}"
                                    class="flex items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                                >
                                    <flux:icon.trash class="h-4 w-4" />
                                    {{ __('people.delete') }}
                                </button>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Quality Indicator -->
        <div class="border-t border-zinc-200 pt-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <flux:heading size="sm" class="text-zinc-700">{{ __('people.data_completeness') }}</flux:heading>
                    @php
                        $completeness = $person->getDataCompleteness();
                        $qualityColor = $completeness >= 80 ? 'green' : ($completeness >= 60 ? 'yellow' : 'red');
                    @endphp
                    <div class="flex items-center gap-2">
                        <div class="w-32 bg-zinc-200 rounded-full h-2">
                            <div class="bg-{{ $qualityColor }}-500 h-2 rounded-full" style="width: {{ $completeness }}%"></div>
                        </div>
                        <span class="text-sm font-medium text-zinc-900">{{ $completeness }}%</span>
                    </div>
                </div>
                
                @if($person->hasIssues())
                    <flux:button wire:click="showDataIssues" variant="outline" icon="exclamation-triangle" size="sm">
                        {{ __('people.view_issues') }}
                    </flux:button>
                @endif
            </div>
        </div>
    </flux:card>

    <!-- Tabbed Content -->
    <div class="space-y-6">
        <flux:tabs wire:model="activeTab">
            <flux:tab name="overview">{{ __('people.overview') }}</flux:tab>
            <flux:tab name="contact">{{ __('people.contact_info') }}</flux:tab>
            <flux:tab name="location">{{ __('people.location_details') }}</flux:tab>
            <flux:tab name="leadership">{{ __('people.leadership_info') }}</flux:tab>
            <flux:tab name="activity">{{ __('people.activity_history') }}</flux:tab>
            <flux:tab name="relationships">{{ __('people.relationships') }}</flux:tab>
        </flux:tabs>

        <!-- Overview Tab -->
        @if($activeTab === 'overview')
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Personal Information -->
                <div class="lg:col-span-2">
                    <flux:card class="space-y-6">
                        <div class="border-b border-zinc-200 pb-4">
                            <flux:heading size="lg" class="text-zinc-900">{{ __('people.personal_information') }}</flux:heading>
                        </div>

                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.first_name') }}</dt>
                                <dd class="mt-1 text-sm font-medium text-zinc-900">{{ $person->first_name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.last_name') }}</dt>
                                <dd class="mt-1 text-sm font-medium text-zinc-900">{{ $person->last_name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.document_number') }}</dt>
                                <dd class="mt-1 text-sm font-mono font-medium text-zinc-900">{{ $person->document_number }}</dd>
                            </div>
                            @if($person->birth_date)
                                <div>
                                    <dt class="text-sm font-medium text-zinc-500">{{ __('people.birth_date') }}</dt>
                                    <dd class="mt-1 text-sm font-medium text-zinc-900">
                                        {{ $person->birth_date->format('d/m/Y') }}
                                        @if($person->age)
                                            <span class="text-zinc-500">({{ $person->age }} {{ __('people.years_old') }})</span>
                                        @endif
                                    </dd>
                                </div>
                            @endif
                            @if($person->gender)
                                <div>
                                    <dt class="text-sm font-medium text-zinc-500">{{ __('people.gender') }}</dt>
                                    <dd class="mt-1 text-sm font-medium text-zinc-900">
                                        {{ __('people.' . strtolower($person->gender === 'M' ? 'male' : ($person->gender === 'F' ? 'female' : 'other'))) }}
                                    </dd>
                                </div>
                            @endif
                            <div>
                                <dt class="text-sm font-medium text-zinc-500">{{ __('people.registration_date') }}</dt>
                                <dd class="mt-1 text-sm font-medium text-zinc-900">{{ $person->created_at->format('d/m/Y H:i') }}</dd>
                            </div>
                        </dl>

                        @if($person->notes)
                            <div class="border-t border-zinc-200 pt-6">
                                <dt class="text-sm font-medium text-zinc-500 mb-2">{{ __('people.notes') }}</dt>
                                <dd class="text-sm text-zinc-900 bg-zinc-50 rounded-lg p-4">{{ $person->notes }}</dd>
                            </div>
                        @endif
                    </flux:card>
                </div>

                <!-- Quick Stats -->
                <div class="space-y-6">
                    <!-- Leadership Stats -->
                    @if($person->is_leader_1x10)
                        <flux:card class="space-y-4">
                            <flux:heading size="md" class="text-zinc-900 flex items-center gap-2">
                                <flux:icon.star class="h-5 w-5 text-purple-500" />
                                {{ __('people.leadership_stats') }}
                            </flux:heading>
                            
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-zinc-600">{{ __('people.assigned_people') }}</span>
                                    <span class="font-medium">{{ $person->assigned_people_count }}/10</span>
                                </div>
                                <div class="w-full bg-zinc-200 rounded-full h-2">
                                    <div class="bg-purple-500 h-2 rounded-full" style="width: {{ ($person->assigned_people_count / 10) * 100 }}%"></div>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-zinc-600">{{ __('people.completion_rate') }}</span>
                                    <span class="font-medium">{{ $person->getCompletionRate() }}%</span>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-zinc-600">{{ __('people.performance_score') }}</span>
                                    <div class="flex items-center gap-1">
                                        <span class="font-medium">{{ $person->getPerformanceScore() }}</span>
                                        <flux:icon.star class="h-4 w-4 text-yellow-500" />
                                    </div>
                                </div>
                            </div>
                        </flux:card>
                    @endif

                    <!-- Contact Summary -->
                    <flux:card class="space-y-4">
                        <flux:heading size="md" class="text-zinc-900 flex items-center gap-2">
                            <flux:icon.chat-bubble-left-right class="h-5 w-5 text-blue-500" />
                            {{ __('people.contact_summary') }}
                        </flux:heading>
                        
                        <div class="space-y-3">
                            @if($person->phone)
                                <div class="flex items-center gap-3">
                                    <flux:icon.phone class="h-4 w-4 text-zinc-400" />
                                    <a href="tel:{{ $person->phone }}" class="text-sm text-blue-600 hover:underline">{{ $person->phone }}</a>
                                </div>
                            @endif
                            
                            @if($person->secondary_phone)
                                <div class="flex items-center gap-3">
                                    <flux:icon.phone class="h-4 w-4 text-zinc-400" />
                                    <a href="tel:{{ $person->secondary_phone }}" class="text-sm text-blue-600 hover:underline">{{ $person->secondary_phone }}</a>
                                </div>
                            @endif
                            
                            @if($person->email)
                                <div class="flex items-center gap-3">
                                    <flux:icon.envelope class="h-4 w-4 text-zinc-400" />
                                    <a href="mailto:{{ $person->email }}" class="text-sm text-blue-600 hover:underline">{{ $person->email }}</a>
                                </div>
                            @endif
                            
                            @if(!$person->phone && !$person->email)
                                <div class="text-sm text-zinc-500 italic">{{ __('people.no_contact_info') }}</div>
                            @endif
                        </div>
                    </flux:card>

                    <!-- Location Summary -->
                    <flux:card class="space-y-4">
                        <flux:heading size="md" class="text-zinc-900 flex items-center gap-2">
                            <flux:icon.map-pin class="h-5 w-5 text-green-500" />
                            {{ __('people.location_summary') }}
                        </flux:heading>
                        
                        <div class="space-y-2">
                            @if($person->state)
                                <div class="text-sm">
                                    <span class="font-medium text-zinc-900">{{ $person->state->name }}</span>
                                    @if($person->municipality)
                                        <span class="text-zinc-500"> • {{ $person->municipality->name }}</span>
                                    @endif
                                    @if($person->parish)
                                        <span class="text-zinc-500"> • {{ $person->parish->name }}</span>
                                    @endif
                                </div>
                            @else
                                <div class="text-sm text-zinc-500 italic">{{ __('people.no_location_info') }}</div>
                            @endif
                            
                            @if($person->voting_center)
                                <div class="text-xs text-zinc-600 bg-zinc-50 rounded p-2">
                                    <strong>{{ __('people.voting_center') }}:</strong> {{ $person->voting_center->name }}
                                    @if($person->voting_table)
                                        <br><strong>{{ __('people.voting_table') }}:</strong> {{ $person->voting_table }}
                                    @endif
                                </div>
                            @endif
                        </div>
                    </flux:card>
                </div>
            </div>
        @endif

        <!-- Other tabs content would be added here -->
        @if($activeTab === 'contact')
            <!-- Contact information detailed view -->
        @endif

        @if($activeTab === 'location')
            <!-- Location details with map integration -->
        @endif

        @if($activeTab === 'leadership')
            <!-- Leadership information and assigned people -->
        @endif

        @if($activeTab === 'activity')
            <!-- Activity history and audit log -->
        @endif

        @if($activeTab === 'relationships')
            <!-- Family and network relationships -->
        @endif
    </div>
</div>
