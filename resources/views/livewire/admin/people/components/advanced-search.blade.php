<div class="space-y-8">
    <flux:breadcrumbs>
        <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home" />
        <flux:breadcrumbs.item href="{{ route('admin.people.index') }}">{{ __('people.title') }}</flux:breadcrumbs.item>
        <flux:breadcrumbs.item>{{ __('people.advanced_search') }}</flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <!-- Page Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="space-y-2">
            <flux:heading size="xl" class="text-zinc-900">{{ __('people.advanced_search') }}</flux:heading>
            <flux:subheading class="text-zinc-600">{{ __('people.search_description') }}</flux:subheading>
        </div>

        <div class="flex gap-3">
            <flux:button :href="route('admin.people.index')" variant="outline" icon="arrow-left" size="sm">
                {{ __('global.back') }}
            </flux:button>
        </div>
    </div>

    <!-- Search Form -->
    <flux:card class="space-y-8">
        <div class="border-b border-zinc-200 pb-6">
            <flux:heading size="lg" class="text-zinc-900">{{ __('people.search_filters') }}</flux:heading>
            <flux:subheading class="text-zinc-600 mt-1">{{ __('people.search_filters_description') }}</flux:subheading>
        </div>

        <!-- Filter Sections -->
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            <!-- Personal Data Filters -->
            <div class="space-y-6">
                <div class="border-b border-zinc-200 pb-3">
                    <h4 class="font-semibold text-zinc-900 flex items-center gap-2">
                        <flux:icon.user class="w-5 h-5 text-blue-500" />
                        {{ __('people.personal_data') }}
                    </h4>
                    <p class="text-sm text-zinc-600 mt-1">{{ __('people.personal_data_filters_description') }}</p>
                </div>

                <div class="space-y-4">
                    <flux:field>
                        <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.first_name') }}</flux:label>
                        <flux:input
                            wire:model="firstName"
                            placeholder="{{ __('people.first_name_placeholder') }}"
                            size="sm"
                            class="mt-1"
                        />
                    </flux:field>

                    <flux:field>
                        <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.last_name') }}</flux:label>
                        <flux:input
                            wire:model="lastName"
                            placeholder="{{ __('people.last_name_placeholder') }}"
                            size="sm"
                            class="mt-1"
                        />
                    </flux:field>

                    <flux:field>
                        <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.document_number') }}</flux:label>
                        <flux:input
                            wire:model="documentNumber"
                            placeholder="{{ __('people.document_number_placeholder') }}"
                            size="sm"
                            class="mt-1 font-mono"
                        />
                    </flux:field>

                    <flux:field>
                        <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.email') }}</flux:label>
                        <flux:input
                            type="email"
                            wire:model="email"
                            placeholder="{{ __('people.email_placeholder') }}"
                            size="sm"
                            class="mt-1"
                        />
                    </flux:field>

                    <flux:field>
                        <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.phone') }}</flux:label>
                        <flux:input
                            wire:model="phone"
                            placeholder="{{ __('people.phone_placeholder') }}"
                            type="tel"
                            size="sm"
                            class="mt-1"
                        />
                    </flux:field>
                </div>
            </div>

            <!-- Location Filters -->
            <div class="space-y-6">
                <div class="border-b border-zinc-200 pb-3">
                    <h4 class="font-semibold text-zinc-900 flex items-center gap-2">
                        <flux:icon.map-pin class="w-5 h-5 text-green-500" />
                        {{ __('people.location') }}
                    </h4>
                    <p class="text-sm text-zinc-600 mt-1">{{ __('people.location_filters_description') }}</p>
                </div>

                <div class="space-y-4">
                    <flux:field>
                        <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.state') }}</flux:label>
                        <flux:select wire:model.live="stateId" placeholder="{{ __('people.select_state') }}" class="mt-1">
                            @foreach($states as $state)
                                <flux:select.option value="{{ $state->id }}">{{ $state->name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </flux:field>

                    @if($stateId)
                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.municipality') }}</flux:label>
                            <flux:select wire:model.live="municipalityId" placeholder="{{ __('people.select_municipality') }}" class="mt-1">
                                @foreach($municipalities as $municipality)
                                    <flux:select.option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        </flux:field>
                    @endif

                    @if($municipalityId)
                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.parish') }}</flux:label>
                            <flux:select wire:model.live="parishId" placeholder="{{ __('people.select_parish') }}" class="mt-1">
                                @foreach($parishes as $parish)
                                    <flux:select.option value="{{ $parish->id }}">{{ $parish->name }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        </flux:field>
                    @endif

                    @if($parishId)
                        <flux:field>
                            <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.voting_center') }}</flux:label>
                            <flux:select wire:model="votingCenterId" placeholder="{{ __('people.select_voting_center') }}" class="mt-1">
                                @foreach($votingCenters as $center)
                                    <flux:select.option value="{{ $center->id }}">{{ $center->name }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        </flux:field>
                    @endif
                </div>
            </div>

            <!-- Classification Filters -->
            <div class="space-y-6">
                <div class="border-b border-zinc-200 pb-3">
                    <h4 class="font-semibold text-zinc-900 flex items-center gap-2">
                        <flux:icon.tag class="w-5 h-5 text-purple-500" />
                        {{ __('people.classification') }}
                    </h4>
                    <p class="text-sm text-zinc-600 mt-1">{{ __('people.classification_filters_description') }}</p>
                </div>

                <div class="space-y-4">
                    <flux:field>
                        <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.person_type') }}</flux:label>
                        <flux:select wire:model="personType" placeholder="{{ __('people.all_types') }}" class="mt-1">
                            <flux:select.option value="militant">{{ __('people.militant') }}</flux:select.option>
                            <flux:select.option value="voter">{{ __('people.voter') }}</flux:select.option>
                            <flux:select.option value="sympathizer">{{ __('people.sympathizer') }}</flux:select.option>
                        </flux:select>
                    </flux:field>

                    <flux:field>
                        <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.status') }}</flux:label>
                        <flux:select wire:model="personStatus" placeholder="{{ __('people.all_statuses') }}" class="mt-1">
                            <flux:select.option value="active">{{ __('people.active') }}</flux:select.option>
                            <flux:select.option value="inactive">{{ __('people.inactive') }}</flux:select.option>
                            <flux:select.option value="suspended">{{ __('people.suspended') }}</flux:select.option>
                        </flux:select>
                    </flux:field>

                    <flux:field>
                        <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.leadership_1x10') }}</flux:label>
                        <flux:select wire:model="isLeader1x10" placeholder="{{ __('people.all') }}" class="mt-1">
                            <flux:select.option value="1">{{ __('people.only_leaders_1x10') }}</flux:select.option>
                            <flux:select.option value="0">{{ __('people.non_leaders') }}</flux:select.option>
                        </flux:select>
                    </flux:field>

                    <flux:field>
                        <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.has_leader') }}</flux:label>
                        <flux:select wire:model="hasLeader" placeholder="{{ __('people.all') }}" class="mt-1">
                            <flux:select.option value="1">{{ __('people.with_leader') }}</flux:select.option>
                            <flux:select.option value="0">{{ __('people.without_leader') }}</flux:select.option>
                        </flux:select>
                    </flux:field>

                    <flux:field>
                        <flux:label class="text-sm font-medium text-zinc-700">{{ __('people.has_user') }}</flux:label>
                        <flux:select wire:model="hasUser" placeholder="{{ __('people.all') }}" class="mt-1">
                            <flux:select.option value="1">{{ __('people.with_user') }}</flux:select.option>
                            <flux:select.option value="0">{{ __('people.without_user') }}</flux:select.option>
                        </flux:select>
                    </flux:field>
                </div>
            </div>
                </div>

                <!-- Date Filters -->
                <div class="mt-6 pt-6 border-t border-zinc-200">
                    <h4 class="font-medium text-zinc-900 mb-4">{{ __('people.date_filters') }}</h4>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <flux:field>
                            <flux:label>{{ __('people.birth_date_from') }}</flux:label>
                            <flux:input type="date" wire:model="birthDateFrom" />
                        </flux:field>

                        <flux:field>
                            <flux:label>{{ __('people.birth_date_to') }}</flux:label>
                            <flux:input type="date" wire:model="birthDateTo" />
                        </flux:field>

                        <flux:field>
                            <flux:label>{{ __('people.min_age') }}</flux:label>
                            <flux:input type="number" wire:model="minAge" placeholder="18" min="0" max="120" />
                        </flux:field>

                        <flux:field>
                            <flux:label>{{ __('people.max_age') }}</flux:label>
                            <flux:input type="number" wire:model="maxAge" placeholder="80" min="0" max="120" />
                        </flux:field>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <flux:field>
                            <flux:label>{{ __('people.registration_date_from') }}</flux:label>
                            <flux:input type="date" wire:model="registrationDateFrom" />
                        </flux:field>

                        <flux:field>
                            <flux:label>{{ __('people.registration_date_to') }}</flux:label>
                            <flux:input type="date" wire:model="registrationDateTo" />
                        </flux:field>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-6 pt-6 border-t border-zinc-200 flex flex-wrap gap-3">
                    <flux:button wire:click="search" variant="primary" icon="magnifying-glass">
                        {{ __('people.search') }}
                    </flux:button>

                    <flux:button wire:click="clearFilters" variant="outline" icon="x-mark">
                        {{ __('people.clear_filters') }}
                    </flux:button>

                    @if($showResults && $people->count() > 0)
                        @can('export people')
                            <flux:button wire:click="exportResults" variant="outline" icon="arrow-down-tray">
                                {{ __('people.export_results') }}
                            </flux:button>
                        @endcan
                    @endif

                    <flux:button :href="route('admin.people.index')" variant="ghost">
                        {{ __('global.back') }}
                    </flux:button>
            </div>
        </flux:card>

        <!-- Search Results -->
        @if($showResults)
            <flux:card class="space-y-6">
                <div>
                    <flux:heading size="lg">
                        {{ __('people.search_results') }}
                        @if($people->count() > 0)
                            ({{ $people->total() }} {{ __('people.results_found') }})
                        @endif
                    </flux:heading>
                </div>

                @if($people->count() > 0)
                    <flux:table :paginate="$people">
                        <flux:table.columns>
                            <flux:table.column>{{ __('people.full_name') }}</flux:table.column>
                            <flux:table.column>{{ __('people.document_number') }}</flux:table.column>
                            <flux:table.column>{{ __('people.person_type') }}</flux:table.column>
                            <flux:table.column>{{ __('people.location') }}</flux:table.column>
                            <flux:table.column>{{ __('people.contact') }}</flux:table.column>
                            <flux:table.column>{{ __('people.status') }}</flux:table.column>
                            <flux:table.column>{{ __('global.actions') }}</flux:table.column>
                        </flux:table.columns>
                        <flux:table.rows>
                            @foreach($people as $person)
                                <flux:table.row :key="$person->id">
                                    <flux:table.cell class="flex items-center gap-3">
                                        <flux:avatar size="sm" class="bg-zinc-100 text-zinc-500">
                                            <flux:icon.user class="h-4 w-4" />
                                        </flux:avatar>
                                        <div class="min-w-0 flex-1">
                                            <div class="font-semibold text-zinc-900 truncate">{{ $person->full_name }}</div>
                                            @if($person->is_leader_1x10)
                                                <flux:badge color="purple" size="xs" class="mt-1">{{ __('people.leader_1x10') }}</flux:badge>
                                            @endif
                                        </div>
                                    </flux:table.cell>

                                    <flux:table.cell class="font-mono text-sm font-medium">{{ $person->document_number }}</flux:table.cell>

                                    <flux:table.cell>
                                        <flux:badge
                                            :color="match($person->person_type) {
                                                'militant' => 'red',
                                                'voter' => 'blue',
                                                'sympathizer' => 'green',
                                                default => 'zinc'
                                            }"
                                            size="sm"
                                            inset="top bottom"
                                        >
                                            {{ __('people.' . $person->person_type) }}
                                        </flux:badge>
                                    </flux:table.cell>

                                    <flux:table.cell>
                                        @if($person->state)
                                            <div class="text-sm">
                                                <div class="font-medium text-zinc-900">{{ $person->state->name }}</div>
                                                @if($person->municipality)
                                                    <div class="text-zinc-500">{{ $person->municipality->name }}</div>
                                                @endif
                                            </div>
                                        @else
                                            <span class="text-zinc-400 text-sm">{{ __('people.no_location') }}</span>
                                        @endif
                                    </flux:table.cell>

                                    <flux:table.cell>
                                        <div class="text-sm">
                                            @if($person->phone)
                                                <div class="font-medium text-zinc-900">{{ $person->phone }}</div>
                                            @endif
                                            @if($person->email)
                                                <div class="text-zinc-500 truncate">{{ $person->email }}</div>
                                            @endif
                                        </div>
                                    </flux:table.cell>

                                    <flux:table.cell>
                                        <flux:badge
                                            :color="match($person->status) {
                                                'active' => 'green',
                                                'inactive' => 'yellow',
                                                'suspended' => 'red',
                                                default => 'zinc'
                                            }"
                                            size="sm"
                                            inset="top bottom"
                                        >
                                            {{ __('people.' . $person->status) }}
                                        </flux:badge>
                                    </flux:table.cell>

                                    <flux:table.cell>
                                        <div class="flex items-center justify-end gap-1">
                                            @can('view people')
                                                <flux:button :href="route('admin.people.show', $person)" variant="ghost" icon="eye" size="sm" tooltip="{{ __('people.view') }}">
                                                    <span class="sr-only">{{ __('people.view') }}</span>
                                                </flux:button>
                                            @endcan

                                            @can('update people')
                                                <flux:button :href="route('admin.people.edit', $person)" variant="outline" icon="pencil" size="sm" tooltip="{{ __('people.edit') }}">
                                                    <span class="sr-only">{{ __('people.edit') }}</span>
                                                </flux:button>
                                            @endcan
                                        </div>
                                    </flux:table.cell>
                                </flux:table.row>
                            @endforeach
                        </flux:table.rows>
                    </flux:table>

                    <div>
                        {{ $people->links() }}
                    </div>
                @else
                    <!-- Empty State -->
                    <div class="text-center py-16">
                        <div class="mx-auto h-16 w-16 rounded-full bg-zinc-100 flex items-center justify-center mb-6">
                            <flux:icon.magnifying-glass class="h-8 w-8 text-zinc-400" />
                        </div>
                        <flux:heading size="lg" class="text-zinc-900 mb-3">{{ __('people.no_results') }}</flux:heading>
                        <flux:subheading class="text-zinc-600 mb-6 max-w-md mx-auto">{{ __('people.try_different_filters') }}</flux:subheading>
                        <flux:button wire:click="clearFilters" variant="outline" icon="x-mark">
                            {{ __('people.clear_filters') }}
                        </flux:button>
                    </div>
                @endif
            </flux:card>
        @endif
    </div>
</div>
