<div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="space-y-2">
            <flux:heading size="xl" class="text-zinc-900">{{ __('people.import_export_title') }}</flux:heading>
            <flux:subheading class="text-zinc-600">{{ __('people.import_export_description') }}</flux:subheading>
        </div>

        <div class="flex items-center gap-3">
            <flux:button wire:click="downloadTemplate" variant="outline" icon="arrow-down-tray">
                {{ __('people.download_template') }}
            </flux:button>
            <flux:button wire:click="showImportHistory" variant="ghost" icon="clock">
                {{ __('people.import_history') }}
            </flux:button>
        </div>
    </div>

    <!-- Import Section -->
    <flux:card class="space-y-6">
        <div class="border-b border-zinc-200 pb-4">
            <flux:heading size="lg" class="text-zinc-900">{{ __('people.import_data') }}</flux:heading>
            <flux:subheading class="text-zinc-600 mt-1">{{ __('people.import_data_subtitle') }}</flux:subheading>
        </div>

        <!-- File Upload Area -->
        <div class="space-y-6">
            <div 
                x-data="{ 
                    isDragging: false,
                    handleDrop(e) {
                        this.isDragging = false;
                        const files = e.dataTransfer.files;
                        if (files.length > 0) {
                            $wire.handleFileUpload(files[0]);
                        }
                    }
                }"
                @dragover.prevent="isDragging = true"
                @dragleave.prevent="isDragging = false"
                @drop.prevent="handleDrop"
                :class="isDragging ? 'border-blue-400 bg-blue-50' : 'border-zinc-300'"
                class="border-2 border-dashed rounded-lg p-8 text-center transition-colors"
            >
                <div class="space-y-4">
                    <div class="mx-auto w-16 h-16 bg-zinc-100 rounded-full flex items-center justify-center">
                        <flux:icon.arrow-up-tray class="h-8 w-8 text-zinc-400" />
                    </div>
                    
                    <div class="space-y-2">
                        <flux:heading size="md" class="text-zinc-900">{{ __('people.drag_drop_file') }}</flux:heading>
                        <flux:subheading class="text-zinc-600">{{ __('people.or_click_to_browse') }}</flux:subheading>
                    </div>
                    
                    <div class="space-y-2">
                        <flux:button variant="outline" icon="folder-open">
                            {{ __('people.select_file') }}
                        </flux:button>
                        <div class="text-xs text-zinc-500">
                            {{ __('people.supported_formats_detailed') }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Preview -->
            @if($uploadedFile)
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <flux:icon.document-text class="h-5 w-5 text-blue-600" />
                            </div>
                            <div>
                                <div class="font-medium text-blue-900">{{ $uploadedFile['name'] }}</div>
                                <div class="text-sm text-blue-700">
                                    {{ $uploadedFile['size'] }} • {{ $uploadedFile['rows'] }} {{ __('people.rows_detected') }}
                                </div>
                            </div>
                        </div>
                        <flux:button wire:click="removeFile" variant="ghost" icon="x-mark" size="sm" />
                    </div>
                </div>
            @endif

            <!-- Import Options -->
            @if($uploadedFile)
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <flux:field>
                        <flux:label>{{ __('people.import_mode') }}</flux:label>
                        <flux:select wire:model="importMode">
                            <flux:select.option value="create_only">{{ __('people.create_only') }}</flux:select.option>
                            <flux:select.option value="update_only">{{ __('people.update_only') }}</flux:select.option>
                            <flux:select.option value="create_update">{{ __('people.create_and_update') }}</flux:select.option>
                        </flux:select>
                        <flux:description>{{ __('people.import_mode_description') }}</flux:description>
                    </flux:field>

                    <flux:field>
                        <flux:label>{{ __('people.duplicate_handling') }}</flux:label>
                        <flux:select wire:model="duplicateHandling">
                            <flux:select.option value="skip">{{ __('people.skip_duplicates') }}</flux:select.option>
                            <flux:select.option value="update">{{ __('people.update_duplicates') }}</flux:select.option>
                            <flux:select.option value="create_new">{{ __('people.create_new_anyway') }}</flux:select.option>
                        </flux:select>
                        <flux:description>{{ __('people.duplicate_handling_description') }}</flux:description>
                    </flux:field>

                    <flux:field>
                        <flux:checkbox wire:model="validateData">{{ __('people.validate_data') }}</flux:checkbox>
                        <flux:description>{{ __('people.validate_data_description') }}</flux:description>
                    </flux:field>

                    <flux:field>
                        <flux:checkbox wire:model="sendNotifications">{{ __('people.send_notifications') }}</flux:checkbox>
                        <flux:description>{{ __('people.send_notifications_description') }}</flux:description>
                    </flux:field>
                </div>

                <!-- Column Mapping -->
                <div class="space-y-4">
                    <flux:heading size="md" class="text-zinc-900">{{ __('people.column_mapping') }}</flux:heading>
                    <flux:subheading class="text-zinc-600">{{ __('people.column_mapping_description') }}</flux:subheading>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach($detectedColumns as $index => $column)
                            <div class="flex items-center gap-4 p-3 bg-zinc-50 rounded-lg">
                                <div class="flex-1">
                                    <div class="font-medium text-zinc-900">{{ $column['name'] }}</div>
                                    <div class="text-sm text-zinc-600">{{ __('people.sample') }}: {{ $column['sample'] }}</div>
                                </div>
                                <div class="flex-1">
                                    <flux:select wire:model="columnMapping.{{ $index }}" size="sm">
                                        <flux:select.option value="">{{ __('people.ignore_column') }}</flux:select.option>
                                        <flux:select.option value="first_name">{{ __('people.first_name') }}</flux:select.option>
                                        <flux:select.option value="last_name">{{ __('people.last_name') }}</flux:select.option>
                                        <flux:select.option value="document_number">{{ __('people.document_number') }}</flux:select.option>
                                        <flux:select.option value="email">{{ __('people.email') }}</flux:select.option>
                                        <flux:select.option value="phone">{{ __('people.phone') }}</flux:select.option>
                                        <flux:select.option value="birth_date">{{ __('people.birth_date') }}</flux:select.option>
                                        <flux:select.option value="gender">{{ __('people.gender') }}</flux:select.option>
                                        <flux:select.option value="address">{{ __('people.address') }}</flux:select.option>
                                        <flux:select.option value="person_type">{{ __('people.person_type') }}</flux:select.option>
                                        <flux:select.option value="status">{{ __('people.status') }}</flux:select.option>
                                    </flux:select>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Import Actions -->
                <div class="flex items-center justify-between pt-6 border-t border-zinc-200">
                    <div class="flex items-center gap-4">
                        <flux:button wire:click="validateImport" variant="outline" icon="check-circle">
                            {{ __('people.validate_import') }}
                        </flux:button>
                        @if($validationResults)
                            <div class="text-sm text-zinc-600">
                                {{ __('people.validation_summary', [
                                    'valid' => $validationResults['valid'],
                                    'invalid' => $validationResults['invalid'],
                                    'total' => $validationResults['total']
                                ]) }}
                            </div>
                        @endif
                    </div>
                    
                    <flux:button 
                        wire:click="processImport" 
                        variant="primary" 
                        icon="arrow-up-tray"
                        :disabled="!$validationResults || $validationResults['invalid'] > 0"
                    >
                        {{ __('people.start_import') }}
                    </flux:button>
                </div>
            @endif
        </div>
    </flux:card>

    <!-- Export Section -->
    <flux:card class="space-y-6">
        <div class="border-b border-zinc-200 pb-4">
            <flux:heading size="lg" class="text-zinc-900">{{ __('people.export_data') }}</flux:heading>
            <flux:subheading class="text-zinc-600 mt-1">{{ __('people.export_data_subtitle') }}</flux:subheading>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Export Options -->
            <div class="space-y-6">
                <flux:field>
                    <flux:label>{{ __('people.export_format') }}</flux:label>
                    <flux:select wire:model="exportFormat">
                        <flux:select.option value="csv">{{ __('people.csv_format') }}</flux:select.option>
                        <flux:select.option value="excel">{{ __('people.excel_format') }}</flux:select.option>
                        <flux:select.option value="pdf">{{ __('people.pdf_format') }}</flux:select.option>
                        <flux:select.option value="json">{{ __('people.json_format') }}</flux:select.option>
                    </flux:select>
                </flux:field>

                <flux:field>
                    <flux:label>{{ __('people.export_scope') }}</flux:label>
                    <flux:select wire:model="exportScope">
                        <flux:select.option value="all">{{ __('people.all_people') }}</flux:select.option>
                        <flux:select.option value="filtered">{{ __('people.current_filter') }}</flux:select.option>
                        <flux:select.option value="selected">{{ __('people.selected_people') }}</flux:select.option>
                        <flux:select.option value="custom">{{ __('people.custom_criteria') }}</flux:select.option>
                    </flux:select>
                </flux:field>

                @if($exportScope === 'custom')
                    <div class="space-y-4 p-4 bg-zinc-50 rounded-lg">
                        <flux:field>
                            <flux:label>{{ __('people.person_type') }}</flux:label>
                            <flux:select wire:model="exportFilters.person_type" multiple>
                                <flux:select.option value="militant">{{ __('people.militant') }}</flux:select.option>
                                <flux:select.option value="voter">{{ __('people.voter') }}</flux:select.option>
                                <flux:select.option value="sympathizer">{{ __('people.sympathizer') }}</flux:select.option>
                            </flux:select>
                        </flux:field>

                        <flux:field>
                            <flux:label>{{ __('people.status') }}</flux:label>
                            <flux:select wire:model="exportFilters.status" multiple>
                                <flux:select.option value="active">{{ __('people.active') }}</flux:select.option>
                                <flux:select.option value="inactive">{{ __('people.inactive') }}</flux:select.option>
                                <flux:select.option value="suspended">{{ __('people.suspended') }}</flux:select.option>
                            </flux:select>
                        </flux:field>

                        <flux:field>
                            <flux:label>{{ __('people.date_range') }}</flux:label>
                            <div class="grid grid-cols-2 gap-2">
                                <flux:input type="date" wire:model="exportFilters.date_from" placeholder="{{ __('people.from_date') }}" />
                                <flux:input type="date" wire:model="exportFilters.date_to" placeholder="{{ __('people.to_date') }}" />
                            </div>
                        </flux:field>
                    </div>
                @endif
            </div>

            <!-- Column Selection -->
            <div class="space-y-6">
                <flux:heading size="md" class="text-zinc-900">{{ __('people.select_columns') }}</flux:heading>
                
                <div class="space-y-3 max-h-64 overflow-y-auto">
                    @foreach($availableColumns as $column => $label)
                        <flux:field>
                            <flux:checkbox wire:model="selectedColumns" value="{{ $column }}">
                                {{ $label }}
                            </flux:checkbox>
                        </flux:field>
                    @endforeach
                </div>

                <div class="flex items-center gap-2 pt-4 border-t border-zinc-200">
                    <flux:button wire:click="selectAllColumns" variant="ghost" size="sm">
                        {{ __('people.select_all') }}
                    </flux:button>
                    <flux:button wire:click="selectNoneColumns" variant="ghost" size="sm">
                        {{ __('people.select_none') }}
                    </flux:button>
                    <flux:button wire:click="selectEssentialColumns" variant="ghost" size="sm">
                        {{ __('people.essential_only') }}
                    </flux:button>
                </div>
            </div>
        </div>

        <!-- Export Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-zinc-200">
            <div class="text-sm text-zinc-600">
                @if($exportScope === 'all')
                    {{ __('people.export_count_all', ['count' => $totalPeopleCount]) }}
                @elseif($exportScope === 'filtered')
                    {{ __('people.export_count_filtered', ['count' => $filteredPeopleCount]) }}
                @elseif($exportScope === 'selected')
                    {{ __('people.export_count_selected', ['count' => count($selectedPeople)]) }}
                @endif
            </div>
            
            <div class="flex items-center gap-3">
                <flux:button wire:click="previewExport" variant="outline" icon="eye">
                    {{ __('people.preview_export') }}
                </flux:button>
                <flux:button wire:click="generateExport" variant="primary" icon="arrow-down-tray">
                    {{ __('people.generate_export') }}
                </flux:button>
            </div>
        </div>
    </flux:card>

    <!-- Import Progress Modal -->
    @if($showImportProgress)
        <flux:modal wire:model="showImportProgress" class="max-w-lg">
            <flux:modal.header>
                <flux:heading size="lg">{{ __('people.import_progress') }}</flux:heading>
            </flux:modal.header>

            <flux:modal.body class="space-y-6">
                <div class="text-center">
                    <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                        <flux:icon.arrow-up-tray class="h-8 w-8 text-blue-600 animate-pulse" />
                    </div>
                    <flux:heading size="md" class="text-zinc-900">{{ __('people.processing_import') }}</flux:heading>
                    <flux:subheading class="text-zinc-600 mt-1">{{ __('people.please_wait') }}</flux:subheading>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between text-sm">
                        <span>{{ __('people.progress') }}</span>
                        <span>{{ $importProgress['current'] }}/{{ $importProgress['total'] }}</span>
                    </div>
                    <div class="w-full bg-zinc-200 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: {{ ($importProgress['current'] / $importProgress['total']) * 100 }}%"></div>
                    </div>
                </div>

                @if($importProgress['errors'])
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-start gap-3">
                            <flux:icon.exclamation-triangle class="h-5 w-5 text-red-500 mt-0.5" />
                            <div>
                                <flux:heading size="sm" class="text-red-900">{{ __('people.import_errors') }}</flux:heading>
                                <div class="text-sm text-red-700 mt-1">
                                    {{ __('people.errors_found', ['count' => count($importProgress['errors'])]) }}
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </flux:modal.body>
        </flux:modal>
    @endif
</div>
