<div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="space-y-2">
            <flux:heading size="xl" class="text-zinc-900">{{ __('people.leadership_management') }}</flux:heading>
            <flux:subheading class="text-zinc-600">{{ __('people.leadership_management_description') }}</flux:subheading>
        </div>

        <div class="flex items-center gap-3">
            <flux:button wire:click="showCreateLeaderModal" variant="primary" icon="plus">
                {{ __('people.create_leader') }}
            </flux:button>
            <flux:button wire:click="showLeadershipReport" variant="outline" icon="chart-bar">
                {{ __('people.leadership_report') }}
            </flux:button>
            <flux:button wire:click="exportLeadershipData" variant="outline" icon="arrow-down-tray">
                {{ __('people.export_data') }}
            </flux:button>
        </div>
    </div>

    <!-- Leadership Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <flux:card class="space-y-4">
            <div class="flex items-center gap-3">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <flux:icon.star class="h-6 w-6 text-purple-600" />
                </div>
                <div>
                    <flux:heading size="sm" class="text-zinc-600">{{ __('people.total_leaders') }}</flux:heading>
                    <flux:heading size="xl" class="text-zinc-900">{{ $leadershipStats['total_leaders'] }}</flux:heading>
                </div>
            </div>
        </flux:card>

        <flux:card class="space-y-4">
            <div class="flex items-center gap-3">
                <div class="p-2 bg-green-100 rounded-lg">
                    <flux:icon.users class="h-6 w-6 text-green-600" />
                </div>
                <div>
                    <flux:heading size="sm" class="text-zinc-600">{{ __('people.people_assigned') }}</flux:heading>
                    <flux:heading size="xl" class="text-zinc-900">{{ $leadershipStats['total_assigned'] }}</flux:heading>
                </div>
            </div>
        </flux:card>

        <flux:card class="space-y-4">
            <div class="flex items-center gap-3">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <flux:icon.chart-bar class="h-6 w-6 text-blue-600" />
                </div>
                <div>
                    <flux:heading size="sm" class="text-zinc-600">{{ __('people.avg_completion') }}</flux:heading>
                    <flux:heading size="xl" class="text-zinc-900">{{ $leadershipStats['avg_completion'] }}%</flux:heading>
                </div>
            </div>
        </flux:card>

        <flux:card class="space-y-4">
            <div class="flex items-center gap-3">
                <div class="p-2 bg-orange-100 rounded-lg">
                    <flux:icon.exclamation-triangle class="h-6 w-6 text-orange-600" />
                </div>
                <div>
                    <flux:heading size="sm" class="text-zinc-600">{{ __('people.needs_attention') }}</flux:heading>
                    <flux:heading size="xl" class="text-zinc-900">{{ $leadershipStats['needs_attention'] }}</flux:heading>
                </div>
            </div>
        </flux:card>
    </div>

    <!-- Filters and Search -->
    <flux:card class="space-y-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div class="flex-1 max-w-md">
                <flux:input
                    wire:model.live.debounce.300ms="search"
                    placeholder="{{ __('people.search_leaders') }}"
                    icon="magnifying-glass"
                />
            </div>
            
            <div class="flex items-center gap-3">
                <flux:select wire:model.live="filterPerformance" placeholder="{{ __('people.all_performance') }}" size="sm">
                    <flux:select.option value="excellent">{{ __('people.excellent') }} (90%+)</flux:select.option>
                    <flux:select.option value="good">{{ __('people.good') }} (70-89%)</flux:select.option>
                    <flux:select.option value="average">{{ __('people.average') }} (50-69%)</flux:select.option>
                    <flux:select.option value="poor">{{ __('people.poor') }} (<50%)</flux:select.option>
                </flux:select>
                
                <flux:select wire:model.live="filterCapacity" placeholder="{{ __('people.all_capacity') }}" size="sm">
                    <flux:select.option value="full">{{ __('people.full_capacity') }} (10/10)</flux:select.option>
                    <flux:select.option value="available">{{ __('people.has_capacity') }} (<10)</flux:select.option>
                    <flux:select.option value="empty">{{ __('people.no_assignments') }} (0)</flux:select.option>
                </flux:select>
                
                <flux:select wire:model.live="filterLocation" placeholder="{{ __('people.all_locations') }}" size="sm">
                    @foreach($states as $state)
                        <flux:select.option value="{{ $state->id }}">{{ $state->name }}</flux:select.option>
                    @endforeach
                </flux:select>
            </div>
        </div>
    </flux:card>

    <!-- Leaders Table -->
    <flux:card class="space-y-6">
        <div class="border-b border-zinc-200 pb-4">
            <flux:heading size="lg" class="text-zinc-900">{{ __('people.leadership_directory') }}</flux:heading>
            <flux:subheading class="text-zinc-600 mt-1">{{ __('people.leadership_directory_description') }}</flux:subheading>
        </div>

        <flux:table :paginate="$leaders">
            <flux:table.columns>
                <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">
                    {{ __('people.leader_name') }}
                </flux:table.column>
                <flux:table.column>{{ __('people.location') }}</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'assigned_count'" :direction="$sortDirection" wire:click="sort('assigned_count')">
                    {{ __('people.assigned_people') }}
                </flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'completion_rate'" :direction="$sortDirection" wire:click="sort('completion_rate')">
                    {{ __('people.completion_rate') }}
                </flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'performance_score'" :direction="$sortDirection" wire:click="sort('performance_score')">
                    {{ __('people.performance') }}
                </flux:table.column>
                <flux:table.column>{{ __('people.last_activity') }}</flux:table.column>
                <flux:table.column>{{ __('global.actions') }}</flux:table.column>
            </flux:table.columns>
            <flux:table.rows>
                @foreach($leaders as $leader)
                    <flux:table.row>
                        <flux:table.cell class="flex items-center gap-3">
                            <flux:avatar size="sm" class="bg-gradient-to-br from-purple-500 to-blue-600 text-white">
                                {{ strtoupper(substr($leader->first_name, 0, 1) . substr($leader->last_name, 0, 1)) }}
                            </flux:avatar>
                            <div>
                                <div class="font-semibold text-zinc-900">
                                    <a href="{{ route('admin.people.show', $leader) }}" class="hover:text-blue-600">
                                        {{ $leader->full_name }}
                                    </a>
                                </div>
                                <div class="text-sm text-zinc-500">{{ $leader->email }}</div>
                                @if($leader->phone)
                                    <div class="text-sm text-zinc-500">{{ $leader->phone }}</div>
                                @endif
                            </div>
                        </flux:table.cell>
                        
                        <flux:table.cell>
                            @if($leader->state)
                                <div class="text-sm">
                                    <div class="font-medium text-zinc-900">{{ $leader->state->name }}</div>
                                    @if($leader->municipality)
                                        <div class="text-zinc-500">{{ $leader->municipality->name }}</div>
                                    @endif
                                </div>
                            @else
                                <span class="text-zinc-400 text-sm">{{ __('people.no_location') }}</span>
                            @endif
                        </flux:table.cell>
                        
                        <flux:table.cell>
                            <div class="flex items-center gap-2">
                                <span class="font-medium">{{ $leader->assigned_people_count }}/10</span>
                                <div class="w-16 bg-zinc-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: {{ ($leader->assigned_people_count / 10) * 100 }}%"></div>
                                </div>
                                @if($leader->assigned_people_count < 10)
                                    <flux:badge size="xs" color="green" variant="outline">
                                        {{ 10 - $leader->assigned_people_count }} {{ __('people.available') }}
                                    </flux:badge>
                                @endif
                            </div>
                        </flux:table.cell>
                        
                        <flux:table.cell>
                            @php
                                $completionRate = $leader->getCompletionRate();
                                $completionColor = $completionRate >= 80 ? 'green' : ($completionRate >= 60 ? 'yellow' : 'red');
                            @endphp
                            <flux:badge size="sm" :color="$completionColor">
                                {{ $completionRate }}%
                            </flux:badge>
                        </flux:table.cell>
                        
                        <flux:table.cell>
                            <div class="flex items-center gap-2">
                                @php
                                    $performanceScore = $leader->getPerformanceScore();
                                    $stars = min(5, max(1, round($performanceScore / 20)));
                                @endphp
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <flux:icon.star class="h-4 w-4 {{ $i <= $stars ? 'text-yellow-500' : 'text-zinc-300' }}" />
                                    @endfor
                                </div>
                                <span class="text-sm font-medium text-zinc-900">{{ $performanceScore }}</span>
                            </div>
                        </flux:table.cell>
                        
                        <flux:table.cell>
                            <span class="text-sm text-zinc-600">{{ $leader->last_activity_at?->diffForHumans() ?? __('people.no_activity') }}</span>
                        </flux:table.cell>
                        
                        <flux:table.cell>
                            <div class="flex items-center gap-1">
                                <flux:button 
                                    wire:click="showLeaderDetails({{ $leader->id }})" 
                                    variant="ghost" 
                                    icon="eye" 
                                    size="sm"
                                    tooltip="{{ __('people.view_details') }}"
                                />
                                <flux:button 
                                    wire:click="showAssignPeopleModal({{ $leader->id }})" 
                                    variant="outline" 
                                    icon="user-plus" 
                                    size="sm"
                                    tooltip="{{ __('people.assign_people') }}"
                                />
                                <flux:button 
                                    wire:click="showLeaderPerformance({{ $leader->id }})" 
                                    variant="ghost" 
                                    icon="chart-bar" 
                                    size="sm"
                                    tooltip="{{ __('people.view_performance') }}"
                                />
                            </div>
                        </flux:table.cell>
                    </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </flux:card>

    <!-- Leadership Hierarchy Visualization -->
    <flux:card class="space-y-6">
        <div class="border-b border-zinc-200 pb-4">
            <flux:heading size="lg" class="text-zinc-900">{{ __('people.leadership_hierarchy') }}</flux:heading>
            <flux:subheading class="text-zinc-600 mt-1">{{ __('people.leadership_hierarchy_description') }}</flux:subheading>
        </div>

        <div class="space-y-6">
            <!-- Hierarchy Controls -->
            <div class="flex items-center gap-4">
                <flux:select wire:model.live="hierarchyView" size="sm">
                    <flux:select.option value="tree">{{ __('people.tree_view') }}</flux:select.option>
                    <flux:select.option value="network">{{ __('people.network_view') }}</flux:select.option>
                    <flux:select.option value="geographic">{{ __('people.geographic_view') }}</flux:select.option>
                </flux:select>
                
                <flux:select wire:model.live="hierarchyFilter" size="sm">
                    <flux:select.option value="all">{{ __('people.all_leaders') }}</flux:select.option>
                    <flux:select.option value="active">{{ __('people.active_only') }}</flux:select.option>
                    <flux:select.option value="full_capacity">{{ __('people.full_capacity_only') }}</flux:select.option>
                </flux:select>
                
                <flux:button wire:click="exportHierarchy" variant="outline" icon="arrow-down-tray" size="sm">
                    {{ __('people.export_hierarchy') }}
                </flux:button>
            </div>

            <!-- Hierarchy Visualization -->
            <div class="bg-zinc-50 rounded-lg p-6 min-h-96">
                @if($hierarchyView === 'tree')
                    <!-- Tree View -->
                    <div class="space-y-4">
                        @foreach($hierarchyData as $topLeader)
                            <div class="bg-white rounded-lg p-4 border border-zinc-200">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center gap-3">
                                        <flux:avatar size="sm" class="bg-gradient-to-br from-purple-500 to-blue-600 text-white">
                                            {{ strtoupper(substr($topLeader['first_name'], 0, 1) . substr($topLeader['last_name'], 0, 1)) }}
                                        </flux:avatar>
                                        <div>
                                            <div class="font-semibold text-zinc-900">{{ $topLeader['full_name'] }}</div>
                                            <div class="text-sm text-zinc-500">{{ $topLeader['location'] }}</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <flux:badge size="sm" color="purple">{{ __('people.leader') }}</flux:badge>
                                        <span class="text-sm text-zinc-600">{{ $topLeader['assigned_count'] }}/10</span>
                                    </div>
                                </div>
                                
                                @if(count($topLeader['assigned_people']) > 0)
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 ml-8">
                                        @foreach($topLeader['assigned_people'] as $person)
                                            <div class="flex items-center gap-2 p-2 bg-zinc-50 rounded">
                                                <flux:avatar size="xs" class="bg-zinc-300 text-zinc-600">
                                                    {{ strtoupper(substr($person['first_name'], 0, 1) . substr($person['last_name'], 0, 1)) }}
                                                </flux:avatar>
                                                <div class="flex-1 min-w-0">
                                                    <div class="text-sm font-medium text-zinc-900 truncate">{{ $person['full_name'] }}</div>
                                                    <div class="text-xs text-zinc-500">{{ __('people.' . $person['person_type']) }}</div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="ml-8 text-sm text-zinc-500 italic">{{ __('people.no_assigned_people') }}</div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @elseif($hierarchyView === 'network')
                    <!-- Network View Placeholder -->
                    <div class="flex items-center justify-center h-64">
                        <div class="text-center">
                            <flux:icon.share class="mx-auto h-12 w-12 text-zinc-400 mb-4" />
                            <flux:heading size="md" class="text-zinc-600">{{ __('people.network_visualization') }}</flux:heading>
                            <flux:subheading class="text-zinc-500 mt-1">{{ __('people.network_coming_soon') }}</flux:subheading>
                        </div>
                    </div>
                @else
                    <!-- Geographic View Placeholder -->
                    <div class="flex items-center justify-center h-64">
                        <div class="text-center">
                            <flux:icon.map class="mx-auto h-12 w-12 text-zinc-400 mb-4" />
                            <flux:heading size="md" class="text-zinc-600">{{ __('people.geographic_visualization') }}</flux:heading>
                            <flux:subheading class="text-zinc-500 mt-1">{{ __('people.map_coming_soon') }}</flux:subheading>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </flux:card>

    <!-- Performance Analytics -->
    <flux:card class="space-y-6">
        <div class="border-b border-zinc-200 pb-4">
            <flux:heading size="lg" class="text-zinc-900">{{ __('people.performance_analytics') }}</flux:heading>
            <flux:subheading class="text-zinc-600 mt-1">{{ __('people.performance_analytics_description') }}</flux:subheading>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Performance Distribution -->
            <div class="space-y-4">
                <flux:heading size="md" class="text-zinc-900">{{ __('people.performance_distribution') }}</flux:heading>
                
                @foreach($performanceDistribution as $level => $data)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 rounded-full bg-{{ $data['color'] }}-500"></div>
                            <span class="font-medium text-zinc-900">{{ __('people.' . $level) }}</span>
                        </div>
                        <div class="flex items-center gap-4">
                            <span class="text-sm text-zinc-600">{{ $data['count'] }} {{ __('people.leaders') }}</span>
                            <div class="w-24 bg-zinc-200 rounded-full h-2">
                                <div class="bg-{{ $data['color'] }}-500 h-2 rounded-full" style="width: {{ $data['percentage'] }}%"></div>
                            </div>
                            <span class="text-sm font-medium text-zinc-900 w-12 text-right">{{ $data['percentage'] }}%</span>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Capacity Utilization -->
            <div class="space-y-4">
                <flux:heading size="md" class="text-zinc-900">{{ __('people.capacity_utilization') }}</flux:heading>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-zinc-600">{{ __('people.total_capacity') }}</span>
                        <span class="font-medium">{{ $capacityStats['total_capacity'] }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-zinc-600">{{ __('people.used_capacity') }}</span>
                        <span class="font-medium">{{ $capacityStats['used_capacity'] }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-zinc-600">{{ __('people.available_capacity') }}</span>
                        <span class="font-medium text-green-600">{{ $capacityStats['available_capacity'] }}</span>
                    </div>
                    <div class="w-full bg-zinc-200 rounded-full h-3">
                        <div class="bg-blue-500 h-3 rounded-full" style="width: {{ ($capacityStats['used_capacity'] / $capacityStats['total_capacity']) * 100 }}%"></div>
                    </div>
                    <div class="text-center text-sm text-zinc-600">
                        {{ round(($capacityStats['used_capacity'] / $capacityStats['total_capacity']) * 100, 1) }}% {{ __('people.utilization') }}
                    </div>
                </div>
            </div>
        </div>
    </flux:card>
</div>
