<?php

namespace App\Livewire\Admin\People\Pages;

use App\Models\Person;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use Illuminate\Contracts\View\View;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Session;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class People extends Component
{
    use LivewireAlert;
    use WithPagination;

    /** @var array<string,string> */
    protected $listeners = [
        'personDeleted' => '$refresh',
        'personCreated' => '$refresh',
        'personUpdated' => '$refresh',
    ];

    #[Session]
    public int $perPage = 15;

    /** @var array<int,string> */
    public array $searchableFields = ['first_name', 'last_name', 'document_number', 'email', 'phone'];

    #[Url]
    public string $search = '';

    #[Url]
    public string $personType = '';

    #[Url]
    public string $personStatus = '';

    #[Url]
    public ?int $stateId = null;

    #[Url]
    public ?int $municipalityId = null;

    #[Url]
    public ?int $parishId = null;

    #[Url]
    public string $isLeader = '';

    #[Url]
    public string $activeTab = 'all';

    #[Url]
    public string $sortBy = 'first_name';

    #[Url]
    public string $sortDirection = 'asc';

    // Quick filters
    public bool $showQuickFilters = false;
    public string $quickFilterType = '';
    public string $quickFilterStatus = '';
    public ?int $quickFilterState = null;
    public string $quickFilterLeadership = '';

    // Saved filters
    public ?int $savedFilter = null;

    // Bulk operations
    public array $selectedPeople = [];
    public bool $selectAll = false;
    public array $bulkEditData = [];

    // Filter management
    public string $filterName = '';
    public string $filterDescription = '';
    public bool $filterIsPublic = false;

    public function mount(): void
    {
        $this->authorize('view people');
    }

    public function sort(string $field): void
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }

        $this->resetPage();
    }

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingPersonType(): void
    {
        $this->resetPage();
    }

    public function updatingPersonStatus(): void
    {
        $this->resetPage();
    }

    public function updatingStateId(): void
    {
        $this->municipalityId = null;
        $this->parishId = null;
        $this->resetPage();
    }

    public function updatingMunicipalityId(): void
    {
        $this->parishId = null;
        $this->resetPage();
    }

    public function updatingParishId(): void
    {
        $this->resetPage();
    }

    public function updatingActiveTab(): void
    {
        $this->resetPage();

        // Clear specific filters when changing tabs
        $this->personType = '';
        $this->isLeader = '';
    }

    public function setActiveTab(string $tab): void
    {
        $this->activeTab = $tab;
        $this->resetPage();

        // Adjust filters according to tab
        switch ($tab) {
            case 'militants':
                $this->personType = 'militant';
                break;
            case 'voters':
                $this->personType = 'voter';
                break;
            case 'sympathizers':
                $this->personType = 'sympathizer';
                break;
            case 'leaders':
                $this->isLeader = '1';
                $this->personType = '';
                break;
            default:
                $this->personType = '';
                $this->isLeader = '';
        }
    }

    public function clearFilters(): void
    {
        $this->search = '';
        $this->personType = '';
        $this->personStatus = '';
        $this->stateId = null;
        $this->municipalityId = null;
        $this->parishId = null;
        $this->isLeader = '';
        $this->activeTab = 'all';
        $this->resetPage();
    }

    public function deletePerson(int $personId): void
    {
        $this->authorize('delete people');

        $person = Person::findOrFail($personId);

        // Check if has assigned people
        if ($person->assignedPeople()->count() > 0) {
            $this->alert('error', 'Cannot delete a person who has other people assigned');
            return;
        }

        try {
            $person->delete();
            $this->alert('success', __('people.person_deleted'));
            $this->dispatch('personDeleted');
        } catch (\Exception $e) {
            $this->alert('error', __('people.error_deleting_person'));
        }
    }

    public function toggleLeaderStatus(int $personId): void
    {
        $this->authorize('update people');

        $person = Person::findOrFail($personId);

        try {
            $person->update([
                'is_leader_1x10' => !$person->is_leader_1x10,
                'updated_by' => auth()->id(),
            ]);

            $status = $person->is_leader_1x10 ? 'activado' : 'desactivado';
            $this->alert('success', "Liderazgo 1x10 {$status} exitosamente");
            $this->dispatch('personUpdated');
        } catch (\Exception $e) {
            $this->alert('error', __('people.error_updating_person'));
        }
    }

    public function updatePriority(int $personId, string $priority): void
    {
        $this->authorize('update people');

        $person = Person::findOrFail($personId);

        try {
            $person->update([
                'priority' => $priority,
                'updated_by' => auth()->id(),
            ]);

            $this->alert('success', 'Prioridad actualizada exitosamente');
            $this->dispatch('personUpdated');
        } catch (\Exception $e) {
            $this->alert('error', __('people.error_updating_person'));
        }
    }

    public function updateStatus(int $personId, string $status): void
    {
        $this->authorize('update people');

        $person = Person::findOrFail($personId);

        try {
            $person->update([
                'status' => $status,
                'updated_by' => auth()->id(),
            ]);

            $this->alert('success', 'Estado actualizado exitosamente');
            $this->dispatch('personUpdated');
        } catch (\Exception $e) {
            $this->alert('error', __('people.error_updating_person'));
        }
    }

    public function exportPeople(): void
    {
        $this->authorize('export people');

        // Here you would implement the export logic
        $this->alert('info', __('people.export_in_development'));
    }

    public function getMunicipalitiesProperty()
    {
        if (!$this->stateId) {
            return collect();
        }

        return Municipality::where('state_id', $this->stateId)
            ->active()
            ->orderBy('name')
            ->get();
    }

    public function getParishesProperty()
    {
        if (!$this->municipalityId) {
            return collect();
        }

        return Parish::where('municipality_id', $this->municipalityId)
            ->active()
            ->orderBy('name')
            ->get();
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        $query = Person::query()
            ->with(['state', 'municipality', 'parish', 'votingCenter', 'assignedLeader', 'user'])
            ->when($this->search, function ($query, $search) {
                $query->searchText($search);
            })
            ->when($this->personStatus, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($this->stateId, function ($query, $stateId) {
                $query->where('state_id', $stateId);
            })
            ->when($this->municipalityId, function ($query, $municipalityId) {
                $query->where('municipality_id', $municipalityId);
            })
            ->when($this->parishId, function ($query, $parishId) {
                $query->where('parish_id', $parishId);
            });

        // Apply filters according to active tab
        switch ($this->activeTab) {
            case 'militants':
                $query->where('person_type', 'militant');
                break;
            case 'voters':
                $query->where('person_type', 'voter');
                break;
            case 'sympathizers':
                $query->where('person_type', 'sympathizer');
                break;
            case 'leaders':
                $query->where('is_leader_1x10', true);
                break;
            default:
                // For "all" tab, apply additional filters if defined
                if ($this->personType) {
                    $query->where('person_type', $this->personType);
                }
                if ($this->isLeader === '1') {
                    $query->where('is_leader_1x10', true);
                } elseif ($this->isLeader === '0') {
                    $query->where('is_leader_1x10', false);
                }
        }

        // Apply sorting
        $sortField = match($this->sortBy) {
            'name' => 'first_name',
            'document' => 'document_number',
            'type' => 'person_type',
            'status' => 'status',
            'created_at' => 'created_at',
            default => 'first_name'
        };

        $people = $query->orderBy($sortField, $this->sortDirection)
            ->when($sortField !== 'first_name', function($query) {
                return $query->orderBy('first_name')->orderBy('last_name');
            })
            ->paginate($this->perPage);

        // Statistics for tabs
        $stats = [
            'total' => Person::count(),
            'militants' => Person::militants()->count(),
            'voters' => Person::voters()->count(),
            'sympathizers' => Person::sympathizers()->count(),
            'leaders' => Person::leaders1x10()->count(),
        ];

        return view('livewire.admin.people.pages.people', [
            'people' => $people,
            'states' => State::active()->orderBy('name')->get(),
            'municipalities' => $this->municipalities,
            'parishes' => $this->parishes,
            'stats' => $stats,
        ]);
    }
}
