<?php

return [
    // Module titles and descriptions
    'title' => 'Gestión de Personas',
    'title_description' => 'Administra toda la información relacionada con las personas registradas en el sistema',
    'create_description' => 'Completa la información para registrar una nueva persona en el sistema',

    // Navigation and menus
    'listing' => 'Listado',
    'add_person' => 'Agregar Persona',
    'advanced_search' => 'Búsqueda Avanzada',
    'person_details' => 'Detalles de la Persona',
    'edit_person' => 'Editar Persona',

    // Tabs and categories
    'all' => 'Todas',
    'militants' => 'Militantes',
    'voters' => 'Votantes',
    'sympathizers' => 'Simpatizantes',
    'leaders_1x10' => 'Líderes 1x10',

    // Campos de datos personales
    'first_name' => 'Nombre',
    'last_name' => 'Apellido',
    'full_name' => 'Nombre Completo',
    'document_number' => 'Número de Documento',
    'birth_date' => 'Fecha de Nacimiento',
    'age' => 'Edad',
    'gender' => 'Género',
    'phone' => 'Teléfono',
    'secondary_phone' => 'Teléfono Secundario',
    'email' => 'Correo Electrónico',
    'address' => 'Dirección',

    // Opciones de género
    'male' => 'Masculino',
    'female' => 'Femenino',
    'other' => 'Otro',

    // Ubicación geográfica
    'location' => 'Ubicación',
    'state' => 'Estado',
    'municipality' => 'Municipio',
    'parish' => 'Parroquia',
    'select_state' => 'Seleccionar Estado',
    'select_municipality' => 'Seleccionar Municipio',
    'select_parish' => 'Seleccionar Parroquia',

    // Información electoral
    'electoral_info' => 'Información Electoral',
    'voting_center' => 'Centro de Votación',
    'voting_table' => 'Mesa de Votación',
    'select_voting_center' => 'Seleccionar Centro de Votación',

    // Tipo de persona y rol
    'person_type' => 'Tipo de Persona',
    'militant' => 'Militante',
    'voter' => 'Votante',
    'sympathizer' => 'Simpatizante',
    'is_leader_1x10' => 'Es Líder 1x10',
    'assigned_leader' => 'Líder Asignado',
    'select_leader' => 'Seleccionar Líder',

    // Estado
    'status' => 'Estado',
    'person_status' => 'Estado de la Persona',
    'active' => 'Activo',
    'inactive' => 'Inactivo',
    'suspended' => 'Suspendido',

    // Acciones
    'save' => 'Guardar',
    'update' => 'Actualizar',
    'cancel' => 'Cancelar',
    'delete' => 'Eliminar',
    'view_details' => 'Ver Detalles',
    'edit' => 'Editar',
    'create_user' => 'Crear Usuario',
    'assign_leader' => 'Asignar Líder',
    'export' => 'Exportar',
    'import' => 'Importar',

    // Filtros y búsqueda
    'search' => 'Buscar',
    'filter_by' => 'Filtrar por',
    'clear_filters' => 'Limpiar Filtros',
    'search_by_name' => 'Buscar por nombre, documento o teléfono',
    'filter_by_location' => 'Filtrar por ubicación',
    'filter_by_role' => 'Filtrar por rol',
    'filter_by_status' => 'Filtrar por estado',
    'filter_by_leadership' => 'Filtrar por liderazgo',
    'only_leaders_1x10' => 'Solo líderes 1x10',
    'non_leaders' => 'No líderes',

    // Mensajes
    'no_results' => 'No se encontraron personas con los filtros aplicados',
    'person_created' => 'Persona creada con éxito',
    'person_updated' => 'Persona actualizada con éxito',
    'person_deleted' => 'Persona eliminada con éxito',
    'user_created' => 'Usuario del sistema creado con éxito',
    'leader_assigned' => 'Líder asignado con éxito',

    // Validaciones
    'first_name_required' => 'El nombre es requerido',
    'last_name_required' => 'El apellido es requerido',
    'document_number_required' => 'El número de documento es requerido',
    'document_number_unique' => 'El número de documento ya existe',
    'email_unique' => 'El correo electrónico ya existe',
    'invalid_birth_date' => 'La fecha de nacimiento es inválida',
    'invalid_phone_format' => 'El formato de teléfono es inválido',

    // Liderazgo 1x10
    'leadership_1x10' => 'Liderazgo 1x10',
    'assigned_people' => 'Personas Asignadas',
    'available_spaces' => 'Espacios Disponibles',
    'max_people_reached' => 'Número máximo de personas alcanzado',
    'leader_without_spaces' => 'El líder seleccionado no tiene espacios disponibles',
    'cannot_be_own_leader' => 'Una persona no puede ser su propio líder',

    // Información adicional
    'notes' => 'Notas',
    'additional_data' => 'Datos Adicionales',
    'system_user' => 'Usuario del Sistema',
    'has_user' => 'Tiene Usuario',
    'no_user' => 'No tiene Usuario',
    'created_at' => 'Creado en',
    'updated_at' => 'Actualizado en',

    // Estadísticas
    'total_people' => 'Total de Personas',
    'total_militants' => 'Total de Militantes',
    'total_voters' => 'Total de Votantes',
    'total_sympathizers' => 'Total de Simpatizantes',
    'total_leaders' => 'Total de Líderes',
    'active_people' => 'Personas Activas',

    // Exportar/Importar
    'export_in_development' => 'Funcionalidad de exportación en desarrollo',
    'import_people' => 'Importar Personas',
    'download_template' => 'Descargar Plantilla',
    'upload_file' => 'Subir Archivo',

    // Permisos
    'view_people' => 'Ver Personas',
    'create_people' => 'Crear Personas',
    'update_people' => 'Actualizar Personas',
    'delete_people' => 'Eliminar Personas',
    'export_people' => 'Exportar Personas',
    'import_people_permission' => 'Importar Personas',
    'assign_leaders' => 'Asignar Líderes',
    'create_users_from_people' => 'Crear Usuarios desde Personas',

    // Nuevos campos agregados
    'priority' => 'Prioridad',
    'low_priority' => 'Baja',
    'medium_priority' => 'Media',
    'high_priority' => 'Alta',
    'urgent_priority' => 'Urgente',
    'all_priorities' => 'Todas las Prioridades',
    'filter_by_priority' => 'Filtrar por Prioridad',

    // Calidad y rendimiento
    'quality_score' => 'Puntuación de Calidad',
    'performance_score' => 'Puntuación de Rendimiento',
    'total_recruits' => 'Total de Reclutados',
    'active_recruits' => 'Reclutados Activos',
    'recruitment_capacity' => 'Capacidad de Reclutamiento',

    // Información de reclutamiento
    'recruitment_information' => 'Información de Reclutamiento',
    'recruitment_date' => 'Fecha de Reclutamiento',
    'recruitment_source' => 'Fuente de Reclutamiento',
    'campaign_id' => 'ID de Campaña',
    'referral_code' => 'Código de Referido',
    'last_activity_date' => 'Última Actividad',

    // Preferencias de contacto
    'preferred_contact_method' => 'Método de Contacto Preferido',
    'contact_frequency' => 'Frecuencia de Contacto',
    'best_contact_time' => 'Mejor Hora de Contacto',

    // Métodos de contacto
    'phone_contact' => 'Teléfono',
    'whatsapp' => 'WhatsApp',
    'email_contact' => 'Correo Electrónico',
    'visit' => 'Visita Personal',
    'social_media' => 'Redes Sociales',

    // Frecuencia de contacto
    'daily' => 'Diario',
    'weekly' => 'Semanal',
    'biweekly' => 'Quincenal',
    'monthly' => 'Mensual',
    'as_needed' => 'Según Necesidad',

    // Fuentes de reclutamiento
    'door_to_door' => 'Puerta a Puerta',
    'phone_call' => 'Llamada Telefónica',
    'social_media_source' => 'Redes Sociales',
    'event' => 'Evento',
    'referral' => 'Referido',
    'conversion' => 'Conversión',
    'other_source' => 'Otro',

    // Mensajes de error adicionales
    'error_creating_person' => 'Error al crear la persona',
    'error_updating_person' => 'Error al actualizar la persona',
    'error_deleting_person' => 'Error al eliminar la persona',
    'no_people_found' => 'No se encontraron personas',

    // Confirmaciones
    'confirm_delete' => '¿Estás seguro de que deseas eliminar esta persona?',
    'confirm_make_leader' => '¿Estás seguro de que deseas hacer a esta persona líder 1x10?',
    'confirm_remove_leader' => '¿Estás seguro de que deseas quitar el liderazgo 1x10 a esta persona?',

    // Estados de actividad
    'recent_activity' => 'Actividad Reciente',
    'moderate_activity' => 'Actividad Moderada',
    'old_activity' => 'Actividad Antigua',
    'very_old_activity' => 'Actividad Muy Antigua',
    'no_activity' => 'Sin Actividad',

    // Estados de capacidad
    'capacity_available' => 'Capacidad Disponible',
    'capacity_half_full' => 'Media Capacidad',
    'capacity_almost_full' => 'Casi Lleno',
    'capacity_full' => 'Capacidad Completa',

    // Niveles de rendimiento
    'excellent' => 'Excelente',
    'very_good' => 'Muy Bueno',
    'good' => 'Bueno',
    'regular' => 'Regular',
    'low' => 'Bajo',

    // Etiquetas adicionales de formulario
    'personal_data' => 'Datos Personales',
    'contact_info' => 'Información de Contacto',
    'role_system' => 'Rol en el Sistema',
    'additional_info' => 'Información Adicional',
    'select_gender' => 'Seleccionar Género',
    'years_old' => 'años',
    'status_and_type' => 'Estado y Tipo',
    'username' => 'Nombre de Usuario',
    'timestamps' => 'Fechas',
    'confirm_delete' => '¿Está seguro de que desea eliminar esta persona?',
    'view' => 'Ver',

    // Búsqueda avanzada
    'search_description' => 'Utiliza filtros avanzados para encontrar personas específicas en el sistema',
    'search_filters' => 'Filtros de Búsqueda',
    'classification' => 'Clasificación',
    'all_types' => 'Todos los Tipos',
    'all_statuses' => 'Todos los Estados',
    'with_leader' => 'Con Líder',
    'without_leader' => 'Sin Líder',
    'with_user' => 'Con Usuario',
    'without_user' => 'Sin Usuario',
    'date_filters' => 'Filtros de Fecha',
    'birth_date_from' => 'Fecha de Nacimiento Desde',
    'birth_date_to' => 'Fecha de Nacimiento Hasta',
    'min_age' => 'Edad Mínima',
    'max_age' => 'Edad Máxima',
    'registration_date_from' => 'Fecha de Registro Desde',
    'registration_date_to' => 'Fecha de Registro Hasta',
    'export_results' => 'Exportar Resultados',
    'search_results' => 'Resultados de Búsqueda',
    'results_found' => 'resultados encontrados',
    'try_different_filters' => 'Intenta ajustar tus filtros de búsqueda',
    'leader_1x10' => 'Líder 1x10',
    'contact' => 'Contacto',

    // Mensajes de error
    'document_number_already_exists' => 'Ya existe una persona con este número de documento',
    'email_already_exists' => 'Ya existe una persona con este correo electrónico',
    'leader_no_spaces' => 'El líder seleccionado no tiene espacios disponibles',
    'cannot_be_own_leader' => 'Una persona no puede ser su propio líder',
    'error_creating_person' => 'Error al crear la persona',
    'error_updating_person' => 'Error al actualizar la persona',
    'error_deleting_person' => 'Error al eliminar la persona',
    'person_already_has_user' => 'Esta persona ya tiene un usuario del sistema',
    'person_needs_email_for_user' => 'La persona necesita una dirección de correo electrónico para crear un usuario del sistema',
    'cannot_delete_person_with_assigned_people' => 'No se puede eliminar una persona que tiene otras personas asignadas',
    'user_created_successfully' => 'Usuario del sistema creado con éxito',
    'error_creating_user' => 'Error al crear el usuario del sistema',

    // Etiquetas y descripciones mejoradas de la interfaz
    'create_description' => 'Agregar una nueva persona al sistema con información completa',
    'personal_data_description' => 'Información personal básica e identificación',
    'contact_info_description' => 'Números de teléfono, correo electrónico e información de dirección',
    'classification_description' => 'Tipo de persona y estado actual en el sistema',
    'leadership_description' => 'Roles de liderazgo y asignaciones de equipo',
    'additional_info_description' => 'Notas adicionales y observaciones',
    'form_actions' => 'Acciones del Formulario',
    'save_person' => 'Guardar Persona',
    'showing_results' => 'Mostrando :from a :to de :total resultados',
    'no_results_description' => 'No se encontraron personas que coincidan con tus criterios. Intenta ajustar tu búsqueda o agregar la primera persona.',
    'add_first_person' => 'Agregar Primera Persona',
    'search_filters_description' => 'Usa los filtros a continuación para encontrar personas específicas en el sistema',
    'personal_data_filters_description' => 'Buscar por nombre, documento, correo o teléfono',

    // Textos de marcador de posición
    'first_name_placeholder' => 'Ingresa el primer nombre',
    'last_name_placeholder' => 'Ingresa el apellido',
    'document_number_placeholder' => 'ej., V-12345678',
    'email_placeholder' => '<EMAIL>',
    'phone_placeholder' => '+58 ************',
    'secondary_phone_placeholder' => '+58 ************',
    'address_placeholder' => 'Ingresa la dirección completa',
    'notes_placeholder' => 'Notas adicionales u observaciones sobre esta persona',
    'leader_1x10_description' => 'Esta persona puede liderar un equipo de hasta 10 personas',

    // Missing translations from views
    'search_by_name_enhanced' => 'Buscar por nombre, documento, email o teléfono',
    'quick_filters' => 'Filtros Rápidos',
    'saved_filters' => 'Filtros Guardados',
    'all_states' => 'Todos los Estados',
    'only_leaders' => 'Solo Líderes',
    'save_filter' => 'Guardar Filtro',
    'filter_results_count' => 'Mostrando :count resultados',
    'selected_count' => ':count seleccionados',
    'clear_selection' => 'Limpiar Selección',
    'bulk_edit' => 'Edición Masiva',
    'bulk_status_change' => 'Cambio de Estado Masivo',
    'bulk_export' => 'Exportación Masiva',
    'bulk_delete' => 'Eliminación Masiva',
    'confirm_bulk_delete' => '¿Estás seguro de que deseas eliminar :count personas?',
    'import_data' => 'Importar Datos',
    'export_all' => 'Exportar Todo',
    'customize_columns' => 'Personalizar Columnas',
    'data_quality' => 'Calidad de Datos',
    'has_phone' => 'Tiene Teléfono',
    'quick_actions' => 'Acciones Rápidas',
    'confirm_delete_single' => '¿Estás seguro de que deseas eliminar esta persona?',
    'data_issues' => 'Problemas de Datos',

    // Bulk operations
    'bulk_edit_title' => 'Edición Masiva',
    'bulk_edit_description' => 'Editar :count personas seleccionadas',
    'no_change' => 'Sin cambios',
    'notes_append' => 'Agregar Notas',
    'notes_append_placeholder' => 'Estas notas se agregarán a las existentes',
    'apply_changes' => 'Aplicar Cambios',

    // Import/Export
    'import_data_title' => 'Importar Datos',
    'import_data_description' => 'Importar personas desde un archivo CSV o Excel',
    'drag_drop_file' => 'Arrastra y suelta tu archivo aquí',
    'supported_formats' => 'Formatos soportados: CSV, Excel (.xlsx)',
    'select_file' => 'Seleccionar Archivo',
    'import_requirements' => 'Requisitos de Importación',
    'import_req_1' => 'El archivo debe contener las columnas requeridas',
    'import_req_2' => 'Los números de documento deben ser únicos',
    'import_req_3' => 'Los emails deben tener formato válido',
    'view_import_guide' => 'Ver Guía de Importación',
    'start_import' => 'Iniciar Importación',

    // Filter management
    'save_filter_title' => 'Guardar Filtro',
    'save_filter_description' => 'Guarda la configuración actual de filtros para uso futuro',
    'filter_name' => 'Nombre del Filtro',
    'filter_name_placeholder' => 'Ej: Militantes de Caracas',
    'filter_description' => 'Descripción del Filtro',
    'filter_description_placeholder' => 'Describe qué personas incluye este filtro',
    'make_filter_public' => 'Hacer filtro público para otros usuarios',

    // Form fields
    'personal_data_description' => 'Información básica de identificación de la persona',
    'contact_info_description' => 'Datos para comunicarse con la persona',
    'location_description' => 'Ubicación geográfica y electoral de la persona',
    'classification_description' => 'Tipo y estado de la persona en el sistema',
    'leadership_description' => 'Configuración del sistema de liderazgo 1x10',
    'additional_info_description' => 'Notas y observaciones sobre la persona',
    'form_actions_description' => 'Guardar o cancelar los cambios realizados',
    'voting_table_placeholder' => 'Ej: Mesa 1',
    'available_spaces' => 'espacios disponibles',

    // Success/Error messages for new functionality
    'no_people_selected' => 'No hay personas seleccionadas',
    'bulk_edit_success' => 'Personas editadas exitosamente',
    'bulk_edit_error' => 'Error al editar personas',
    'bulk_delete_success' => 'Personas eliminadas exitosamente',
    'bulk_delete_error' => 'Error al eliminar personas',
    'filter_saved' => 'Filtro guardado exitosamente',
    'duplicate_in_development' => 'Funcionalidad de duplicación en desarrollo',
    'bulk_export_in_development' => 'Exportación masiva en desarrollo',
    'export_all_in_development' => 'Exportación completa en desarrollo',
    'bulk_status_in_development' => 'Cambio de estado masivo en desarrollo',
    'columns_customization_in_development' => 'Personalización de columnas en desarrollo',
    'import_in_development' => 'Funcionalidad de importación en desarrollo',
];
