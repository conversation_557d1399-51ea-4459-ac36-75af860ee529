<?php

return [
    'title' => 'People Management',
    'title_description' => 'Manage all information related to people registered in the system',

    // Navigation and menus
    'listing' => 'Listing',
    'add_person' => 'Add Person',
    'advanced_search' => 'Advanced Search',
    'person_details' => 'Person Details',
    'edit_person' => 'Edit Person',

    // Tabs
    'all' => 'All',
    'militants' => 'Militants',
    'voters' => 'Voters',
    'sympathizers' => 'Sympathizers',
    'leaders_1x10' => '1x10 Leaders',

    // Personal data fields
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'full_name' => 'Full Name',
    'document_number' => 'Document Number',
    'birth_date' => 'Birth Date',
    'age' => 'Age',
    'gender' => 'Gender',
    'phone' => 'Phone',
    'secondary_phone' => 'Secondary Phone',
    'email' => 'Email',
    'address' => 'Address',

    // Gender options
    'male' => 'Male',
    'female' => 'Female',
    'other' => 'Other',

    // Geographic location
    'location' => 'Location',
    'state' => 'State',
    'municipality' => 'Municipality',
    'parish' => 'Parish',
    'select_state' => 'Select State',
    'select_municipality' => 'Select Municipality',
    'select_parish' => 'Select Parish',

    // Electoral information
    'electoral_info' => 'Electoral Information',
    'voting_center' => 'Voting Center',
    'voting_table' => 'Voting Table',
    'select_voting_center' => 'Select Voting Center',

    // Person type and role
    'person_type' => 'Person Type',
    'militant' => 'Militant',
    'voter' => 'Voter',
    'sympathizer' => 'Sympathizer',
    'is_leader_1x10' => 'Is 1x10 Leader',
    'assigned_leader' => 'Assigned Leader',
    'select_leader' => 'Select Leader',

    // Status
    'status' => 'Status',
    'person_status' => 'Person Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'suspended' => 'Suspended',

    // Actions
    'save' => 'Save',
    'update' => 'Update',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'view_details' => 'View Details',
    'edit' => 'Edit',
    'create_user' => 'Create User',
    'assign_leader' => 'Assign Leader',
    'export' => 'Export',
    'import' => 'Import',

    // Filters and search
    'search' => 'Search',
    'filter_by' => 'Filter by',
    'clear_filters' => 'Clear Filters',
    'search_by_name' => 'Search by name, document or phone',
    'filter_by_location' => 'Filter by location',
    'filter_by_role' => 'Filter by role',
    'filter_by_status' => 'Filter by status',
    'filter_by_leadership' => 'Filter by leadership',
    'only_leaders_1x10' => 'Only 1x10 leaders',
    'non_leaders' => 'Non-leaders',

    // Messages
    'no_results' => 'No people found with the applied filters',
    'person_created' => 'Person created successfully',
    'person_updated' => 'Person updated successfully',
    'person_deleted' => 'Person deleted successfully',
    'user_created' => 'System user created successfully',
    'leader_assigned' => 'Leader assigned successfully',

    // Validations
    'first_name_required' => 'First name is required',
    'last_name_required' => 'Last name is required',
    'document_number_required' => 'Document number is required',
    'document_number_unique' => 'Document number already exists',
    'email_unique' => 'Email already exists',
    'invalid_birth_date' => 'Invalid birth date',
    'invalid_phone_format' => 'Invalid phone format',

    // 1x10 Leadership
    'leadership_1x10' => '1x10 Leadership',
    'assigned_people' => 'Assigned People',
    'available_spaces' => 'Available Spaces',
    'max_people_reached' => 'Maximum number of people reached',
    'leader_without_spaces' => 'Selected leader has no available spaces',
    'cannot_be_own_leader' => 'A person cannot be their own leader',

    // Additional information
    'notes' => 'Notes',
    'additional_data' => 'Additional Data',
    'system_user' => 'System User',
    'has_user' => 'Has User',
    'no_user' => 'No User',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',

    // Statistics
    'total_people' => 'Total People',
    'total_militants' => 'Total Militants',
    'total_voters' => 'Total Voters',
    'total_sympathizers' => 'Total Sympathizers',
    'total_leaders' => 'Total Leaders',
    'active_people' => 'Active People',

    // Export/Import
    'export_in_development' => 'Export functionality in development',
    'import_people' => 'Import People',
    'download_template' => 'Download Template',
    'upload_file' => 'Upload File',

    // Permissions
    'view_people' => 'View People',
    'create_people' => 'Create People',
    'update_people' => 'Update People',
    'delete_people' => 'Delete People',
    'export_people' => 'Export People',
    'import_people_permission' => 'Import People',
    'assign_leaders' => 'Assign Leaders',
    'create_users_from_people' => 'Create Users from People',

    // Additional form labels
    'personal_data' => 'Personal Data',
    'contact_info' => 'Contact Information',
    'role_system' => 'Role in System',
    'additional_info' => 'Additional Information',
    'select_gender' => 'Select Gender',
    'years_old' => 'years old',
    'status_and_type' => 'Status and Type',
    'username' => 'Username',
    'timestamps' => 'Timestamps',
    'confirm_delete' => 'Are you sure you want to delete this person?',
    'view' => 'View',

    // Advanced search
    'search_description' => 'Use advanced filters to find specific people in the system',
    'search_filters' => 'Search Filters',
    'classification' => 'Classification',
    'all_types' => 'All Types',
    'all_statuses' => 'All Statuses',
    'with_leader' => 'With Leader',
    'without_leader' => 'Without Leader',
    'with_user' => 'With User',
    'without_user' => 'Without User',
    'date_filters' => 'Date Filters',
    'birth_date_from' => 'Birth Date From',
    'birth_date_to' => 'Birth Date To',
    'min_age' => 'Minimum Age',
    'max_age' => 'Maximum Age',
    'registration_date_from' => 'Registration Date From',
    'registration_date_to' => 'Registration Date To',
    'export_results' => 'Export Results',
    'search_results' => 'Search Results',
    'results_found' => 'results found',
    'try_different_filters' => 'Try adjusting your search filters',
    'leader_1x10' => '1x10 Leader',
    'contact' => 'Contact',

    // Error messages
    'document_number_already_exists' => 'A person with this document number already exists',
    'email_already_exists' => 'A person with this email already exists',
    'leader_no_spaces' => 'The selected leader has no available spaces',
    'cannot_be_own_leader' => 'A person cannot be their own leader',
    'error_creating_person' => 'Error creating person',
    'error_updating_person' => 'Error updating person',
    'error_deleting_person' => 'Error deleting person',
    'person_already_has_user' => 'This person already has a system user',
    'person_needs_email_for_user' => 'Person needs an email address to create a system user',
    'cannot_delete_person_with_assigned_people' => 'Cannot delete a person who has other people assigned',
    'user_created_successfully' => 'System user created successfully',
    'error_creating_user' => 'Error creating system user',

    // Enhanced UI labels and descriptions
    'create_description' => 'Add a new person to the system with complete information',
    'personal_data_description' => 'Basic personal information and identification',
    'contact_info_description' => 'Phone numbers, email and address information',
    'classification_description' => 'Person type and current status in the system',
    'leadership_description' => 'Leadership roles and team assignments',
    'additional_info_description' => 'Additional notes and observations',
    'form_actions' => 'Form Actions',
    'save_person' => 'Save Person',
    'showing_results' => 'Showing :from to :to of :total results',
    'no_results_description' => 'No people found matching your criteria. Try adjusting your search or add the first person.',
    'add_first_person' => 'Add First Person',
    'search_filters_description' => 'Use the filters below to find specific people in the system',
    'personal_data_filters_description' => 'Search by name, document, email or phone',

    // Placeholder texts
    'first_name_placeholder' => 'Enter first name',
    'last_name_placeholder' => 'Enter last name',
    'document_number_placeholder' => 'e.g., V-12345678',
    'email_placeholder' => '<EMAIL>',
    'phone_placeholder' => '+58 ************',
    'secondary_phone_placeholder' => '+58 ************',
    'address_placeholder' => 'Enter complete address',
    'notes_placeholder' => 'Additional notes or observations about this person',
    'leader_1x10_description' => 'This person can lead a team of up to 10 people',
];
