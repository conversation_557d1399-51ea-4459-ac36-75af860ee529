<?php

return [
    // Module titles and descriptions
    'title' => 'People Management',
    'title_description' => 'Manage all information related to people registered in the system',
    'create_description' => 'Complete the information to register a new person in the system',

    // Navigation and menus
    'listing' => 'Listing',
    'add_person' => 'Add Person',
    'advanced_search' => 'Advanced Search',
    'person_details' => 'Person Details',
    'edit_person' => 'Edit Person',

    // Tabs and categories
    'all' => 'All',
    'militants' => 'Militants',
    'voters' => 'Voters',
    'sympathizers' => 'Sympathizers',
    'leaders_1x10' => '1x10 Leaders',

    // Personal data fields
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'full_name' => 'Full Name',
    'document_number' => 'Document Number',
    'birth_date' => 'Birth Date',
    'age' => 'Age',
    'gender' => 'Gender',
    'phone' => 'Phone',
    'secondary_phone' => 'Secondary Phone',
    'email' => 'Email',
    'address' => 'Address',

    // Gender options
    'male' => 'Male',
    'female' => 'Female',
    'other' => 'Other',

    // Geographic location
    'location' => 'Location',
    'state' => 'State',
    'municipality' => 'Municipality',
    'parish' => 'Parish',
    'select_state' => 'Select State',
    'select_municipality' => 'Select Municipality',
    'select_parish' => 'Select Parish',

    // Electoral information
    'electoral_info' => 'Electoral Information',
    'voting_center' => 'Voting Center',
    'voting_table' => 'Voting Table',
    'select_voting_center' => 'Select Voting Center',

    // Person type and role
    'person_type' => 'Person Type',
    'militant' => 'Militant',
    'voter' => 'Voter',
    'sympathizer' => 'Sympathizer',
    'is_leader_1x10' => 'Is 1x10 Leader',
    'assigned_leader' => 'Assigned Leader',
    'select_leader' => 'Select Leader',

    // Status
    'status' => 'Status',
    'person_status' => 'Person Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'suspended' => 'Suspended',

    // Actions
    'save' => 'Save',
    'update' => 'Update',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'view_details' => 'View Details',
    'edit' => 'Edit',
    'create_user' => 'Create User',
    'assign_leader' => 'Assign Leader',
    'export' => 'Export',
    'import' => 'Import',

    // Filters and search
    'search' => 'Search',
    'filter_by' => 'Filter by',
    'clear_filters' => 'Clear Filters',
    'search_by_name' => 'Search by name, document or phone',
    'filter_by_location' => 'Filter by location',
    'filter_by_role' => 'Filter by role',
    'filter_by_status' => 'Filter by status',
    'filter_by_leadership' => 'Filter by leadership',
    'only_leaders_1x10' => 'Only 1x10 leaders',
    'non_leaders' => 'Non-leaders',

    // Messages
    'no_results' => 'No people found with the applied filters',
    'person_created' => 'Person created successfully',
    'person_updated' => 'Person updated successfully',
    'person_deleted' => 'Person deleted successfully',
    'user_created' => 'System user created successfully',
    'leader_assigned' => 'Leader assigned successfully',

    // Validations
    'first_name_required' => 'First name is required',
    'last_name_required' => 'Last name is required',
    'document_number_required' => 'Document number is required',
    'document_number_unique' => 'Document number already exists',
    'email_unique' => 'Email already exists',
    'invalid_birth_date' => 'Invalid birth date',
    'invalid_phone_format' => 'Invalid phone format',

    // 1x10 Leadership
    'leadership_1x10' => '1x10 Leadership',
    'assigned_people' => 'Assigned People',
    'available_spaces' => 'Available Spaces',
    'max_people_reached' => 'Maximum number of people reached',
    'leader_without_spaces' => 'Selected leader has no available spaces',
    'cannot_be_own_leader' => 'A person cannot be their own leader',

    // Additional information
    'notes' => 'Notes',
    'additional_data' => 'Additional Data',
    'system_user' => 'System User',
    'has_user' => 'Has User',
    'no_user' => 'No User',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',

    // Statistics
    'total_people' => 'Total People',
    'total_militants' => 'Total Militants',
    'total_voters' => 'Total Voters',
    'total_sympathizers' => 'Total Sympathizers',
    'total_leaders' => 'Total Leaders',
    'active_people' => 'Active People',

    // Export/Import
    'export_in_development' => 'Export functionality in development',
    'import_people' => 'Import People',
    'download_template' => 'Download Template',
    'upload_file' => 'Upload File',

    // Permissions
    'view_people' => 'View People',
    'create_people' => 'Create People',
    'update_people' => 'Update People',
    'delete_people' => 'Delete People',
    'export_people' => 'Export People',
    'import_people_permission' => 'Import People',
    'assign_leaders' => 'Assign Leaders',
    'create_users_from_people' => 'Create Users from People',

    // Additional form labels
    'personal_data' => 'Personal Data',
    'contact_info' => 'Contact Information',
    'role_system' => 'Role in System',
    'additional_info' => 'Additional Information',
    'select_gender' => 'Select Gender',
    'years_old' => 'years old',
    'status_and_type' => 'Status and Type',
    'username' => 'Username',
    'timestamps' => 'Timestamps',
    'confirm_delete' => 'Are you sure you want to delete this person?',
    'view' => 'View',

    // Advanced search
    'search_description' => 'Use advanced filters to find specific people in the system',
    'search_filters' => 'Search Filters',
    'classification' => 'Classification',
    'all_types' => 'All Types',
    'all_statuses' => 'All Statuses',
    'with_leader' => 'With Leader',
    'without_leader' => 'Without Leader',
    'with_user' => 'With User',
    'without_user' => 'Without User',
    'date_filters' => 'Date Filters',
    'birth_date_from' => 'Birth Date From',
    'birth_date_to' => 'Birth Date To',
    'min_age' => 'Minimum Age',
    'max_age' => 'Maximum Age',
    'registration_date_from' => 'Registration Date From',
    'registration_date_to' => 'Registration Date To',
    'export_results' => 'Export Results',
    'search_results' => 'Search Results',
    'results_found' => 'results found',
    'try_different_filters' => 'Try adjusting your search filters',
    'leader_1x10' => '1x10 Leader',
    'contact' => 'Contact',

    // Error messages
    'document_number_already_exists' => 'A person with this document number already exists',
    'email_already_exists' => 'A person with this email already exists',
    'leader_no_spaces' => 'The selected leader has no available spaces',
    'cannot_be_own_leader' => 'A person cannot be their own leader',
    'error_creating_person' => 'Error creating person',
    'error_updating_person' => 'Error updating person',
    'error_deleting_person' => 'Error deleting person',
    'person_already_has_user' => 'This person already has a system user',
    'person_needs_email_for_user' => 'Person needs an email address to create a system user',
    'cannot_delete_person_with_assigned_people' => 'Cannot delete a person who has other people assigned',
    'user_created_successfully' => 'System user created successfully',
    'error_creating_user' => 'Error creating system user',

    // Enhanced UI labels and descriptions
    'create_description' => 'Add a new person to the system with complete information',
    'personal_data_description' => 'Basic personal information and identification',
    'contact_info_description' => 'Phone numbers, email and address information',
    'classification_description' => 'Person type and current status in the system',
    'leadership_description' => 'Leadership roles and team assignments',
    'additional_info_description' => 'Additional notes and observations',
    'form_actions' => 'Form Actions',
    'save_person' => 'Save Person',
    'showing_results' => 'Showing :from to :to of :total results',
    'no_results_description' => 'No people found matching your criteria. Try adjusting your search or add the first person.',
    'add_first_person' => 'Add First Person',
    'search_filters_description' => 'Use the filters below to find specific people in the system',
    'personal_data_filters_description' => 'Search by name, document, email or phone',

    // Placeholder texts
    'first_name_placeholder' => 'Enter first name',
    'last_name_placeholder' => 'Enter last name',
    'document_number_placeholder' => 'e.g., V-12345678',
    'email_placeholder' => '<EMAIL>',
    'phone_placeholder' => '+58 ************',
    'secondary_phone_placeholder' => '+58 ************',
    'address_placeholder' => 'Enter complete address',
    'notes_placeholder' => 'Additional notes or observations about this person',
    'leader_1x10_description' => 'This person can lead a team of up to 10 people',

    // Personal data fields
    'personal_data' => 'Personal Data',
    'personal_data_description' => 'Basic identification information of the person',
    'full_name' => 'Full Name',
    'document_number' => 'Document Number',
    'birth_date' => 'Birth Date',
    'gender' => 'Gender',
    'age' => 'Age',

    // Contact information
    'contact_info' => 'Contact Information',
    'contact_info_description' => 'Data to communicate with the person',
    'phone' => 'Phone',
    'secondary_phone' => 'Secondary Phone',
    'email' => 'Email',
    'address' => 'Address',

    // Geographic location
    'location' => 'Location',
    'location_description' => 'Geographic and electoral location of the person',
    'state' => 'State',
    'municipality' => 'Municipality',
    'parish' => 'Parish',
    'voting_center' => 'Voting Center',
    'voting_table' => 'Voting Table',
    'no_location' => 'No location',
    'select_state' => 'Select State',
    'select_municipality' => 'Select Municipality',
    'select_parish' => 'Select Parish',
    'select_voting_center' => 'Select Voting Center',

    // Person types
    'person_type' => 'Person Type',
    'militant' => 'Militant',
    'voter' => 'Voter',
    'sympathizer' => 'Sympathizer',
    'all_types' => 'All types',

    // Status
    'status' => 'Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'suspended' => 'Suspended',
    'all_statuses' => 'All statuses',

    // Leadership 1x10
    'leadership' => 'Leadership',
    'leadership_1x10' => '1x10 Leadership',
    'leadership_description' => '1x10 leadership system configuration',
    'is_leader_1x10' => 'Is 1x10 Leader',
    'leader_1x10' => '1x10 Leader',
    'assigned_leader' => 'Assigned Leader',
    'assigned_people' => 'Assigned People',
    'available_spaces' => 'available spaces',
    'only_leaders' => 'Only leaders',
    'non_leaders' => 'Non-leaders',
    'select_leader' => 'Select Leader',

    // Classification
    'classification' => 'Classification',
    'classification_description' => 'Type and status of the person in the system',

    // Additional information
    'additional_info' => 'Additional Information',
    'additional_info_description' => 'Notes and observations about the person',
    'notes' => 'Notes',
    'additional_data' => 'Additional Data',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'created_by' => 'Created By',
    'updated_by' => 'Updated By',

    // Gender options
    'male' => 'Male',
    'female' => 'Female',
    'other' => 'Other',
    'select_gender' => 'Select gender',

    // Actions
    'save' => 'Save',
    'save_person' => 'Save Person',
    'update' => 'Update',
    'delete' => 'Delete',
    'view_details' => 'View Details',
    'edit' => 'Edit',
    'create_user' => 'Create User',
    'assign_leader' => 'Assign Leader',
    'export' => 'Export',
    'import' => 'Import',
    'call' => 'Call',
    'send_email' => 'Send Email',
    'duplicate' => 'Duplicate',

    // Form actions
    'form_actions' => 'Form Actions',
    'form_actions_description' => 'Save or cancel the changes made',

    // Search and filters
    'search_by_name_enhanced' => 'Search by name, document, email or phone',
    'quick_filters' => 'Quick Filters',
    'saved_filters' => 'Saved Filters',
    'all_states' => 'All States',
    'save_filter' => 'Save Filter',
    'filter_results_count' => 'Showing :count results',
    'clear_filters' => 'Clear Filters',
    'search' => 'Search',
    'filter_by' => 'Filter by',

    // Table and results
    'showing_results' => 'Showing :from to :to of :total results',
    'selected_count' => ':count selected',
    'clear_selection' => 'Clear Selection',
    'data_quality' => 'Data Quality',
    'has_phone' => 'Has Phone',
    'quick_actions' => 'Quick Actions',
    'data_issues' => 'Data Issues',

    // Bulk operations
    'bulk_edit' => 'Bulk Edit',
    'bulk_status_change' => 'Bulk Status Change',
    'bulk_export' => 'Bulk Export',
    'bulk_delete' => 'Bulk Delete',
    'confirm_bulk_delete' => 'Are you sure you want to delete :count people?',
    'bulk_edit_title' => 'Bulk Edit',
    'bulk_edit_description' => 'Edit :count selected people',
    'no_change' => 'No change',
    'notes_append' => 'Append Notes',
    'notes_append_placeholder' => 'These notes will be added to existing ones',
    'apply_changes' => 'Apply Changes',

    // Import/Export
    'import_data' => 'Import Data',
    'export_all' => 'Export All',
    'customize_columns' => 'Customize Columns',
    'import_data_title' => 'Import Data',
    'import_data_description' => 'Import people from a CSV or Excel file',
    'drag_drop_file' => 'Drag and drop your file here',
    'supported_formats' => 'Supported formats: CSV, Excel (.xlsx)',
    'select_file' => 'Select File',
    'import_requirements' => 'Import Requirements',
    'import_req_1' => 'File must contain required columns',
    'import_req_2' => 'Document numbers must be unique',
    'import_req_3' => 'Emails must have valid format',
    'download_template' => 'Download Template',
    'view_import_guide' => 'View Import Guide',
    'start_import' => 'Start Import',

    // Filter management
    'save_filter_title' => 'Save Filter',
    'save_filter_description' => 'Save current filter configuration for future use',
    'filter_name' => 'Filter Name',
    'filter_name_placeholder' => 'e.g., Militants from Caracas',
    'filter_description' => 'Filter Description',
    'filter_description_placeholder' => 'Describe what people this filter includes',
    'make_filter_public' => 'Make filter public for other users',

    // Messages
    'no_results' => 'No people found with applied filters',
    'no_results_description' => 'No people found matching your criteria. Try adjusting your search or add the first person.',
    'add_first_person' => 'Add First Person',
    'person_created' => 'Person created successfully',
    'person_updated' => 'Person updated successfully',
    'person_deleted' => 'Person deleted successfully',
    'user_created' => 'System user created successfully',
    'leader_assigned' => 'Leader assigned successfully',
    'confirm_delete_single' => 'Are you sure you want to delete this person?',

    // Placeholder texts
    'first_name_placeholder' => 'Enter first name',
    'last_name_placeholder' => 'Enter last name',
    'document_number_placeholder' => 'e.g., V-12345678',
    'email_placeholder' => '<EMAIL>',
    'phone_placeholder' => '+58 ************',
    'secondary_phone_placeholder' => '+58 ************',
    'address_placeholder' => 'Enter complete address',
    'notes_placeholder' => 'Additional notes or observations about this person',
    'voting_table_placeholder' => 'e.g., Table 1',

    // Success/Error messages for new functionality
    'no_people_selected' => 'No people selected',
    'bulk_edit_success' => 'People edited successfully',
    'bulk_edit_error' => 'Error editing people',
    'bulk_delete_success' => 'People deleted successfully',
    'bulk_delete_error' => 'Error deleting people',
    'filter_saved' => 'Filter saved successfully',
    'duplicate_in_development' => 'Duplication functionality in development',
    'bulk_export_in_development' => 'Bulk export in development',
    'export_all_in_development' => 'Full export in development',
    'bulk_status_in_development' => 'Bulk status change in development',
    'columns_customization_in_development' => 'Column customization in development',
    'import_in_development' => 'Import functionality in development',
    'export_in_development' => 'Export functionality in development',

    // Validation messages
    'first_name_required' => 'First name is required',
    'last_name_required' => 'Last name is required',
    'document_number_required' => 'Document number is required',
    'document_number_unique' => 'Document number already exists',
    'email_unique' => 'Email already exists',
    'invalid_birth_date' => 'Invalid birth date',
    'invalid_phone_format' => 'Invalid phone format',
    'error_creating_person' => 'Error creating person',
    'error_updating_person' => 'Error updating person',
    'error_deleting_person' => 'Error deleting person',
    'cannot_delete_person_with_assigned_people' => 'Cannot delete a person who has other people assigned',
    'error_creating_user' => 'Error creating system user',
];
